#!/usr/bin/env python3

import sys
from pytubefix import Channel

def explore_channel_methods():
    """Explore all available methods and properties of a Channel object"""
    
    # Test with <PERSON>'s channel
    channel_url = "https://www.youtube.com/@SteveRam"
    
    try:
        print(f"Creating Channel object for: {channel_url}")
        channel = Channel(channel_url)
        
        print(f"\nChannel Name: {channel.channel_name}")
        print(f"Channel URL: {channel.channel_url}")
        
        # Get all attributes and methods
        print("\n=== ALL CHANNEL ATTRIBUTES AND METHODS ===")
        all_attrs = dir(channel)
        for attr in sorted(all_attrs):
            if not attr.startswith('_'):  # Skip private methods
                try:
                    value = getattr(channel, attr)
                    if callable(value):
                        print(f"METHOD: {attr}()")
                    else:
                        print(f"PROPERTY: {attr} = {type(value).__name__}")
                except Exception as e:
                    print(f"ERROR accessing {attr}: {e}")
        
        # Test specific methods that might contain live streams
        print("\n=== TESTING SPECIFIC METHODS ===")
        
        # Test video_urls
        try:
            print("Testing video_urls...")
            video_count = 0
            for video in channel.video_urls:
                video_count += 1
                print(f"Video {video_count}: {getattr(video, 'title', 'No title')} - {getattr(video, 'video_id', 'No ID')}")
                if video_count >= 5:  # Limit to first 5
                    break
            print(f"Total videos found via video_urls: {video_count}")
        except Exception as e:
            print(f"Error with video_urls: {e}")
        
        # Test if there are any stream-related properties
        stream_attrs = [attr for attr in all_attrs if 'stream' in attr.lower() or 'live' in attr.lower()]
        if stream_attrs:
            print(f"\nFound stream/live related attributes: {stream_attrs}")
            for attr in stream_attrs:
                try:
                    value = getattr(channel, attr)
                    print(f"{attr}: {type(value).__name__}")
                    if callable(value):
                        print(f"  Calling {attr}()...")
                        result = value()
                        print(f"  Result: {type(result).__name__}")
                    elif hasattr(value, '__iter__') and not isinstance(value, str):
                        print(f"  Testing iteration over {attr}...")
                        count = 0
                        for item in value:
                            count += 1
                            print(f"    {attr} item {count}: {getattr(item, 'title', 'No title')} - {getattr(item, 'video_id', 'No ID')}")
                            if count >= 3:  # Limit to first 3
                                break
                        print(f"  Total items in {attr}: {count}")
                except Exception as e:
                    print(f"  Error with {attr}: {e}")
        
        # Test different URL variations
        print("\n=== TESTING URL VARIATIONS ===")
        
        # Try streams URL
        streams_url = channel_url + "/streams"
        try:
            print(f"Testing streams URL: {streams_url}")
            streams_channel = Channel(streams_url)
            print(f"Streams channel name: {streams_channel.channel_name}")
            
            stream_video_count = 0
            for video in streams_channel.video_urls:
                stream_video_count += 1
                print(f"Stream Video {stream_video_count}: {getattr(video, 'title', 'No title')}")
                if stream_video_count >= 3:
                    break
            print(f"Videos found via streams URL: {stream_video_count}")
            
        except Exception as e:
            print(f"Error with streams URL: {e}")
        
        # Try live URL
        live_url = channel_url + "/live"
        try:
            print(f"Testing live URL: {live_url}")
            live_channel = Channel(live_url)
            print(f"Live channel name: {live_channel.channel_name}")
        except Exception as e:
            print(f"Error with live URL: {e}")
            
    except Exception as e:
        print(f"Error creating channel: {e}")
        return

if __name__ == "__main__":
    explore_channel_methods()
