---
path:            '/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/SwiftUICore.swiftmodule/arm64e-apple-macos.swiftmodule'
dependencies:
  - mtime:           1746401591000000000
    path:            '/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/SwiftUICore.swiftmodule/arm64e-apple-macos.swiftmodule'
    size:            3707504
  - mtime:           1745034242000000000
    path:            'usr/lib/swift/Swift.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            1929969
    sdk_relative:    true
  - mtime:           1745035158000000000
    path:            'usr/include/_time.apinotes'
    size:            1132
    sdk_relative:    true
  - mtime:           1745043435000000000
    path:            'usr/include/ObjectiveC.apinotes'
    size:            11147
    sdk_relative:    true
  - mtime:           1745030861000000000
    path:            'usr/include/Dispatch.apinotes'
    size:            19
    sdk_relative:    true
  - mtime:           1745047476000000000
    path:            'System/Library/Frameworks/CoreGraphics.framework/Headers/CoreGraphics.apinotes'
    size:            52901
    sdk_relative:    true
  - mtime:           1745376776000000000
    path:            'usr/include/XPC.apinotes'
    size:            123
    sdk_relative:    true
  - mtime:           1745038091000000000
    path:            'System/Library/Frameworks/Security.framework/Headers/Security.apinotes'
    size:            162
    sdk_relative:    true
  - mtime:           1745045809000000000
    path:            'System/Library/Frameworks/Foundation.framework/Headers/Foundation.apinotes'
    size:            81098
    sdk_relative:    true
  - mtime:           1745035393000000000
    path:            'usr/lib/swift/_errno.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            3890
    sdk_relative:    true
  - mtime:           1745035397000000000
    path:            'usr/lib/swift/_time.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            1065
    sdk_relative:    true
  - mtime:           1745035418000000000
    path:            'usr/lib/swift/_signal.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            1101
    sdk_relative:    true
  - mtime:           1745035418000000000
    path:            'usr/lib/swift/sys_time.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            1102
    sdk_relative:    true
  - mtime:           1745035414000000000
    path:            'usr/lib/swift/_stdio.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            1513
    sdk_relative:    true
  - mtime:           1745035426000000000
    path:            'usr/lib/swift/unistd.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            853
    sdk_relative:    true
  - mtime:           1745035393000000000
    path:            'usr/lib/swift/_math.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            15284
    sdk_relative:    true
  - mtime:           1745034435000000000
    path:            'usr/lib/swift/_Builtin_float.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            4262
    sdk_relative:    true
  - mtime:           1745035443000000000
    path:            'usr/lib/swift/Darwin.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            18255
    sdk_relative:    true
  - mtime:           1745035849000000000
    path:            'usr/lib/swift/_Concurrency.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            230631
    sdk_relative:    true
  - mtime:           1745035971000000000
    path:            'usr/lib/swift/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            22908
    sdk_relative:    true
  - mtime:           1745036547000000000
    path:            'System/Library/Frameworks/Combine.framework/Modules/Combine.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            167834
    sdk_relative:    true
  - mtime:           1745036488000000000
    path:            'usr/lib/swift/ObjectiveC.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            6597
    sdk_relative:    true
  - mtime:           1745036728000000000
    path:            'usr/lib/swift/Dispatch.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            57170
    sdk_relative:    true
  - mtime:           1745037002000000000
    path:            'usr/lib/swift/CoreFoundation.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            22859
    sdk_relative:    true
  - mtime:           1745037658000000000
    path:            'usr/lib/swift/XPC.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            33654
    sdk_relative:    true
  - mtime:           1745038520000000000
    path:            'usr/lib/swift/IOKit.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            3651
    sdk_relative:    true
  - mtime:           1745035870000000000
    path:            'usr/lib/swift/Observation.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            3489
    sdk_relative:    true
  - mtime:           1745036593000000000
    path:            'usr/lib/swift/System.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            95504
    sdk_relative:    true
  - mtime:           1746092916000000000
    path:            'System/Library/Frameworks/Foundation.framework/Modules/Foundation.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            995841
    sdk_relative:    true
  - mtime:           1745039975000000000
    path:            'System/Library/Frameworks/CoreGraphics.framework/Modules/CoreGraphics.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            53548
    sdk_relative:    true
  - mtime:           1745629043000000000
    path:            'System/Library/Frameworks/Accessibility.framework/Modules/Accessibility.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            21906
    sdk_relative:    true
  - mtime:           1745039870000000000
    path:            'System/Library/Frameworks/UniformTypeIdentifiers.framework/Headers/UniformTypeIdentifiers.apinotes'
    size:            1666
    sdk_relative:    true
  - mtime:           1745040477000000000
    path:            'usr/lib/swift/UniformTypeIdentifiers.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            20610
    sdk_relative:    true
  - mtime:           1745035158000000000
    path:            'usr/include/os.apinotes'
    size:            1658
    sdk_relative:    true
  - mtime:           1745036886000000000
    path:            'usr/lib/swift/os.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            108065
    sdk_relative:    true
  - mtime:           1745043128000000000
    path:            'System/Library/Frameworks/CoreTransferable.framework/Modules/CoreTransferable.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            22071
    sdk_relative:    true
  - mtime:           1745041116000000000
    path:            'System/Library/Frameworks/DeveloperToolsSupport.framework/Modules/DeveloperToolsSupport.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            10920
    sdk_relative:    true
  - mtime:           1745040352000000000
    path:            'System/Library/Frameworks/CoreText.framework/Headers/CoreText.apinotes'
    size:            1662
    sdk_relative:    true
  - mtime:           1743191765000000000
    path:            'System/Library/Frameworks/Metal.framework/Headers/Metal.apinotes'
    size:            80245
    sdk_relative:    true
  - mtime:           1746092467000000000
    path:            'System/Library/Frameworks/ApplicationServices.framework/Headers/ApplicationServices.apinotes'
    size:            2012
    sdk_relative:    true
  - mtime:           1745046206000000000
    path:            'System/Library/Frameworks/QuartzCore.framework/Headers/QuartzCore.apinotes'
    size:            7428
    sdk_relative:    true
  - mtime:           1745038179000000000
    path:            'usr/lib/swift/Metal.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            25175
    sdk_relative:    true
  - mtime:           1745040441000000000
    path:            'System/Library/Frameworks/CoreText.framework/Modules/CoreText.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            1581
    sdk_relative:    true
  - mtime:           1745041263000000000
    path:            'usr/lib/swift/QuartzCore.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            1812
    sdk_relative:    true
  - mtime:           1745042386000000000
    path:            'System/Library/Frameworks/Symbols.framework/Modules/Symbols.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            21596
    sdk_relative:    true
  - mtime:           1745036615000000000
    path:            'usr/lib/swift/simd.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            229505
    sdk_relative:    true
  - mtime:           1745042940000000000
    path:            'System/Library/Frameworks/SwiftUICore.framework/Modules/SwiftUICore.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            1006825
    sdk_relative:    true
version:         1
...
