{"client": {"name": "basic", "version": 0, "file-system": "device-agnostic", "perform-ownership-analysis": "no"}, "targets": {"": ["<all>"]}, "nodes": {"/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath": {"is-mutated": true}, "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release": {"is-mutated": true}, "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation": {"is-mutated": true}, "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app": {"is-mutated": true}, "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/MacOS/Scripture": {"is-mutated": true}, "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath": {"is-mutated": true}, "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release": {"is-mutated": true}, "<TRIGGER: CodeSign /Users/<USER>/Downloads/Apps/Scripture/Scripture\\ 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app>": {"is-command-timestamp": true}, "<TRIGGER: Ld /Users/<USER>/Downloads/Apps/Scripture/Scripture\\ 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/MacOS/Scripture normal>": {"is-command-timestamp": true}, "<TRIGGER: MkDir /Users/<USER>/Downloads/Apps/Scripture/Scripture\\ 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app>": {"is-command-timestamp": true}, "<TRIGGER: SetMode u+w,go-w,a+rX /Users/<USER>/Downloads/Apps/Scripture/Scripture\\ 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app>": {"is-command-timestamp": true}, "<TRIGGER: SetOwnerAndGroup shrikant:staff /Users/<USER>/Downloads/Apps/Scripture/Scripture\\ 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app>": {"is-command-timestamp": true}, "<TRIGGER: Strip /Users/<USER>/Downloads/Apps/Scripture/Scripture\\ 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/MacOS/Scripture>": {"is-command-timestamp": true}, "<TRIGGER: Validate /Users/<USER>/Downloads/Apps/Scripture/Scripture\\ 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app>": {"is-command-timestamp": true}}, "commands": {"<all>": {"tool": "phony", "inputs": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/MacOS", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/_CodeSignature", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-scanning>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--end>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--linker-inputs-ready>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--modules-ready>", "<workspace-Release-macosx15.5-macos--stale-file-removal>"], "outputs": ["<all>"]}, "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-Release-macosx--arm64-build-headers-stale-file-removal>": {"tool": "stale-file-removal", "expectedOutputs": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/MacOS/Scripture", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/_CodeSignature", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/assetcatalog_output/thinned", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/assetcatalog_dependencies_thinned", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/assetcatalog_generated_info.plist_thinned", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/assetcatalog_output/unthinned", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/assetcatalog_dependencies_unthinned", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/assetcatalog_generated_info.plist_unthinned", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/Resources/download_transcript.py", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/Resources/download_transcript_enhanced.py", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/Resources/get_channel_thumbnail.py", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/Resources/get_channel_videos.py", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/Resources/install_dependencies.py", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/Resources/requirements.txt", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/DerivedSources/GeneratedAssetSymbols-Index.plist", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/Scripture.app.dSYM/Contents/Resources/DWARF/Scripture", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/assetcatalog_generated_info.plist", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/Resources/Assets.car", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/assetcatalog_signature", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/MacOS", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/Resources", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/assetcatalog_output/thinned", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/assetcatalog_output/unthinned", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/Info.plist", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/PkgInfo", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Scripture.app.xcent", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Scripture.app.xcent.der", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/MacOS/Scripture", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloader Swift Compilation Finished", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/Scripture.app", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/Scripture.swiftmodule/arm64-apple-macos.abi.json", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/Scripture.swiftmodule/arm64-apple-macos.swiftdoc", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/Scripture.swiftmodule/arm64-apple-macos.swiftmodule", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/MacOS/Scripture", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture_lto.o", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture_dependency_info.dat", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/DownloadManager.o", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloaderApp.o", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/DownloadManager.stringsdata", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloaderApp.stringsdata", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloader-master.swiftconstvalues", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.swiftmodule", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.swiftsourceinfo", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.abi.json", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture-Swift.h", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.swiftdoc", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/DerivedSources/Scripture-Swift.h", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/DerivedSources/Entitlements.plist", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.LinkFileList", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.SwiftConstValuesFileList", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.SwiftFileList", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloader-OutputFileMap.json", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloader_const_extract_protocols.json", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Scripture-all-non-framework-target-headers.hmap", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Scripture-all-target-headers.hmap", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Scripture-generated-files.hmap", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Scripture-own-target-headers.hmap", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Scripture-project-headers.hmap", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Scripture.DependencyMetadataFileList", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Scripture.DependencyStaticMetadataFileList", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Scripture.hmap", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/empty-Scripture.plist"], "roots": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-Release-macosx--arm64-build-headers-stale-file-removal>"]}, "<workspace-Release-macosx15.5-macos--stale-file-removal>": {"tool": "stale-file-removal", "expectedOutputs": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60-VFS/all-product-headers.yaml"], "outputs": ["<workspace-Release-macosx15.5-macos--stale-file-removal>"]}, "P0:::ClangStatCache /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache": {"tool": "shell", "description": "ClangStatCache /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache", "inputs": [], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache", "<ClangStatCache /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-o", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache"], "env": {}, "always-out-of-date": true, "working-directory": "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader.xcodeproj", "signature": "2f01e9a12fd9a0a68f467a53dd3c76a0"}, "P0:::CreateBuildDirectory /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath", "inputs": [], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath>", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath"]}, "P0:::CreateBuildDirectory /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release", "inputs": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath"], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release>", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release"]}, "P0:::CreateBuildDirectory /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation", "inputs": [], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation>", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation"]}, "P0:::CreateBuildDirectory /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath", "inputs": [], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath>", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath"]}, "P0:::CreateBuildDirectory /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release", "inputs": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath"], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release>", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release"]}, "P0:::Gate /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/Scripture.app.dSYM-target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-": {"tool": "phony", "inputs": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/Scripture.app.dSYM/Contents/Resources/DWARF/Scripture", "<GenerateDSYMFile /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/Scripture.app.dSYM/Contents/Resources/DWARF/Scripture>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/Scripture.app.dSYM/"]}, "P0:::Gate WorkspaceHeaderMapVFSFilesWritten": {"tool": "phony", "inputs": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60-VFS/all-product-headers.yaml"], "outputs": ["<WorkspaceHeaderMapVFSFilesWritten>"]}, "P0:::Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--AppIntentsMetadataTaskProducer": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>", "<ExtractAppIntentsMetadata /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/Resources/Metadata.appintents>", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.SwiftConstValuesFileList", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Scripture.DependencyMetadataFileList", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Scripture.DependencyStaticMetadataFileList"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--AppIntentsMetadataTaskProducer>"]}, "P0:::Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-ChangeAlternatePermissions": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-ChangePermissions>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--will-sign>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-ChangeAlternatePermissions>"]}, "P0:::Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-ChangePermissions": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-StripSymbols>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--will-sign>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>", "<SetMode /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app>", "<SetOwner /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app>"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-ChangePermissions>"]}, "P0:::Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-CodeSign": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-ChangeAlternatePermissions>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--will-sign>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>", "<CodeSign /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app>"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-CodeSign>"]}, "P0:::Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-CopyAside": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-GenerateStubAPI>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--will-sign>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-CopyAside>"]}, "P0:::Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-GenerateStubAPI": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ProductPostprocessingTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-GenerateStubAPI>"]}, "P0:::Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-RegisterExecutionPolicyException": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-CodeSign>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--will-sign>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>", "<RegisterExecutionPolicyException /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app>"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-RegisterExecutionPolicyException>"]}, "P0:::Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-RegisterProduct": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-Validate>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--will-sign>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>", "<LSRegisterURL /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app>", "<Touch /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app>"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-RegisterProduct>"]}, "P0:::Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-StripSymbols": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-CopyAside>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--will-sign>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>", "<Strip /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/MacOS/Scripture>"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-StripSymbols>"]}, "P0:::Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-Validate": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-RegisterExecutionPolicyException>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--will-sign>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>", "<Validate /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app>"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-Validate>"]}, "P0:::Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--CopySwiftPackageResourcesTaskProducer": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--CopySwiftPackageResourcesTaskProducer>"]}, "P0:::Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--CustomTaskProducer": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--CustomTaskProducer>"]}, "P0:::Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--DocumentationTaskProducer": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--DocumentationTaskProducer>"]}, "P0:::Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--GenerateAppPlaygroundAssetCatalogTaskProducer": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--GeneratedFilesTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--GenerateAppPlaygroundAssetCatalogTaskProducer>"]}, "P0:::Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--GeneratedFilesTaskProducer": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ProductStructureTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Scripture.app.xcent", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Scripture.app.xcent.der", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/DerivedSources/Entitlements.plist"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--GeneratedFilesTaskProducer>"]}, "P0:::Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--HeadermapTaskProducer": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--GenerateAppPlaygroundAssetCatalogTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Scripture-all-non-framework-target-headers.hmap", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Scripture-all-target-headers.hmap", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Scripture-generated-files.hmap", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Scripture-own-target-headers.hmap", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Scripture-project-headers.hmap", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Scripture.hmap"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--HeadermapTaskProducer>"]}, "P0:::Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--InfoPlistTaskProducer": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/Info.plist", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/PkgInfo", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/empty-Scripture.plist"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--InfoPlistTaskProducer>"]}, "P0:::Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleMapTaskProducer": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleMapTaskProducer>"]}, "P0:::Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--RealityAssetsTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer>"]}, "P0:::Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ProductPostprocessingTaskProducer": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleMapTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--SwiftPackageCopyFilesTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--InfoPlistTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--SanitizerTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--SwiftStandardLibrariesTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--SwiftFrameworkABICheckerTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--SwiftABIBaselineGenerationTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--TestTargetTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--TestHostTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--CopySwiftPackageResourcesTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--TAPISymbolExtractorTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--DocumentationTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--CustomTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--StubBinaryTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--AppIntentsMetadataTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ProductPostprocessingTaskProducer>"]}, "P0:::Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ProductStructureTaskProducer": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--start>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>", "<MkDir /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app>", "<MkDir /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents>", "<MkDir /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/MacOS>", "<MkDir /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/Resources>", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/Scripture.app"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ProductStructureTaskProducer>"]}, "P0:::Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--RealityAssetsTaskProducer": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--HeadermapTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--RealityAssetsTaskProducer>"]}, "P0:::Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--SanitizerTaskProducer": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--SanitizerTaskProducer>"]}, "P0:::Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--StubBinaryTaskProducer": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--StubBinaryTaskProducer>"]}, "P0:::Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--SwiftABIBaselineGenerationTaskProducer": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--SwiftABIBaselineGenerationTaskProducer>"]}, "P0:::Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--SwiftFrameworkABICheckerTaskProducer": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--SwiftFrameworkABICheckerTaskProducer>"]}, "P0:::Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--SwiftPackageCopyFilesTaskProducer": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--SwiftPackageCopyFilesTaskProducer>"]}, "P0:::Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--SwiftStandardLibrariesTaskProducer": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>", "<CopySwiftStdlib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app>"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--SwiftStandardLibrariesTaskProducer>"]}, "P0:::Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--TAPISymbolExtractorTaskProducer": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--TAPISymbolExtractorTaskProducer>"]}, "P0:::Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--TestHostTaskProducer": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--TestHostTaskProducer>"]}, "P0:::Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--TestTargetPostprocessingTaskProducer": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ProductPostprocessingTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--TestTargetPostprocessingTaskProducer>"]}, "P0:::Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--TestTargetTaskProducer": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--TestTargetTaskProducer>"]}, "P0:::Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--copy-headers-completion": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/DerivedSources/GeneratedAssetSymbols-Index.plist"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--copy-headers-completion>"]}, "P0:::Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--fused-phase0-compile-sources&link-binary&copy-bundle-resources": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/assetcatalog_output/thinned/", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/assetcatalog_dependencies_thinned", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/assetcatalog_generated_info.plist_thinned", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/assetcatalog_output/unthinned/", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/assetcatalog_dependencies_unthinned", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/assetcatalog_generated_info.plist_unthinned", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/Resources/download_transcript.py", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/Resources/download_transcript_enhanced.py", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/Resources/get_channel_thumbnail.py", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/Resources/get_channel_videos.py", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/Resources/install_dependencies.py", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/Resources/requirements.txt", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/DerivedSources/GeneratedAssetSymbols-Index.plist", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/assetcatalog_generated_info.plist", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/Resources/Assets.car", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/assetcatalog_signature", "<MkDir /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/assetcatalog_output/thinned>", "<MkDir /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/assetcatalog_output/unthinned>", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloader Swift Compilation Finished", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/Scripture.swiftmodule/arm64-apple-macos.abi.json", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/Scripture.swiftmodule/arm64-apple-macos.swiftdoc", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/Scripture.swiftmodule/arm64-apple-macos.swiftmodule", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture_lto.o", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture_dependency_info.dat", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/DownloadManager.o", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloaderApp.o", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/DownloadManager.stringsdata", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloaderApp.stringsdata", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloader-master.swiftconstvalues", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.swiftmodule", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.swiftsourceinfo", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.abi.json", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture-Swift.h", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.swiftdoc", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.LinkFileList", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.SwiftFileList", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloader-OutputFileMap.json", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloader_const_extract_protocols.json"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--fused-phase0-compile-sources&link-binary&copy-bundle-resources>"]}, "P0:::Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--generated-headers": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/DerivedSources/GeneratedAssetSymbols-Index.plist"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--generated-headers>"]}, "P0:::Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--swift-generated-headers": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/DownloadManager.o", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloaderApp.o", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/DownloadManager.stringsdata", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloaderApp.stringsdata", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloader-master.swiftconstvalues", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.swiftmodule", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.swiftsourceinfo", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.abi.json", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture-Swift.h", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.swiftdoc", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/DerivedSources/Scripture-Swift.h"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--swift-generated-headers>"]}, "P0:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Release:CodeSign /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app": {"tool": "code-sign-task", "description": "CodeSign /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app", "inputs": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Assets.xcassets/", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/ContentView.swift/", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Managers/DownloadManager.swift/", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Preview Content/Preview Assets.xcassets/", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Python/download_transcript.py/", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Python/download_transcript_enhanced.py/", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Python/get_channel_thumbnail.py/", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Python/get_channel_videos.py/", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Python/install_dependencies.py/", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Python/requirements.txt/", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/YouTubeTranscriptDownloaderApp.swift/", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/Info.plist/", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Scripture.app.xcent/", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-ChangeAlternatePermissions>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--will-sign>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--entry>", "<TRIGGER: SetMode u+w,go-w,a+rX /Users/<USER>/Downloads/Apps/Scripture/Scripture\\ 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app>", "<TRIGGER: Strip /Users/<USER>/Downloads/Apps/Scripture/Scripture\\ 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/MacOS/Scripture>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/_CodeSignature", "<CodeSign /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app>", "<TRIGGER: CodeSign /Users/<USER>/Downloads/Apps/Scripture/Scripture\\ 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app>"]}, "P0:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Release:CompileAssetCatalogVariant thinned /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/Resources /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Assets.xcassets": {"tool": "shell", "description": "CompileAssetCatalogVariant thinned /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/Resources /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Assets.xcassets", "inputs": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Assets.xcassets/", "<MkDir /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/assetcatalog_output/thinned>", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/assetcatalog_output/thinned", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/assetcatalog_output/thinned/", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/assetcatalog_dependencies_thinned", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/assetcatalog_generated_info.plist_thinned"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/actool", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Assets.xcassets", "--compile", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/assetcatalog_output/thinned", "--output-format", "human-readable-text", "--notices", "--warnings", "--export-dependency-info", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/assetcatalog_dependencies_thinned", "--output-partial-info-plist", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/assetcatalog_generated_info.plist_thinned", "--app-icon", "AppIcon", "--accent-color", "AccentColor", "--enable-on-demand-resources", "NO", "--development-region", "en", "--target-device", "mac", "--minimum-deployment-target", "14.0", "--platform", "macosx"], "env": {}, "working-directory": "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/YouTubeTranscriptDownloader", "control-enabled": false, "deps": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/assetcatalog_dependencies_thinned"], "deps-style": "dependency-info", "signature": "b4bea74d142c82307d52877d50dccca4"}, "P0:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Release:CompileAssetCatalogVariant unthinned /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/Resources /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Assets.xcassets": {"tool": "shell", "description": "CompileAssetCatalogVariant unthinned /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/Resources /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Assets.xcassets", "inputs": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Assets.xcassets/", "<MkDir /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/assetcatalog_output/unthinned>", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/assetcatalog_output/unthinned", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/assetcatalog_output/unthinned/", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/assetcatalog_dependencies_unthinned", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/assetcatalog_generated_info.plist_unthinned"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/actool", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Assets.xcassets", "--compile", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/assetcatalog_output/unthinned", "--output-format", "human-readable-text", "--notices", "--warnings", "--export-dependency-info", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/assetcatalog_dependencies_unthinned", "--output-partial-info-plist", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/assetcatalog_generated_info.plist_unthinned", "--app-icon", "AppIcon", "--accent-color", "AccentColor", "--enable-on-demand-resources", "NO", "--development-region", "en", "--target-device", "mac", "--minimum-deployment-target", "14.0", "--platform", "macosx"], "env": {}, "working-directory": "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/YouTubeTranscriptDownloader", "control-enabled": false, "deps": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/assetcatalog_dependencies_unthinned"], "deps-style": "dependency-info", "signature": "92cb00e8e8731cc1fc6f35c4b9087f5a"}, "P0:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Release:CopySwiftLibs /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app": {"tool": "embed-swift-stdlib", "description": "CopySwiftLibs /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app", "inputs": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/MacOS/Scripture", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--immediate>"], "outputs": ["<CopySwiftStdlib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app>"], "deps": "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/SwiftStdLibToolInputDependencies.dep"}, "P0:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Release:CpResource /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/Resources/download_transcript.py /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Python/download_transcript.py": {"tool": "file-copy", "description": "CpResource /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/Resources/download_transcript.py /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Python/download_transcript.py", "inputs": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Python/download_transcript.py/", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--entry>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/Resources/download_transcript.py"]}, "P0:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Release:CpResource /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/Resources/download_transcript_enhanced.py /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Python/download_transcript_enhanced.py": {"tool": "file-copy", "description": "CpResource /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/Resources/download_transcript_enhanced.py /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Python/download_transcript_enhanced.py", "inputs": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Python/download_transcript_enhanced.py/", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--entry>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/Resources/download_transcript_enhanced.py"]}, "P0:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Release:CpResource /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/Resources/get_channel_thumbnail.py /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Python/get_channel_thumbnail.py": {"tool": "file-copy", "description": "CpResource /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/Resources/get_channel_thumbnail.py /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Python/get_channel_thumbnail.py", "inputs": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Python/get_channel_thumbnail.py/", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--entry>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/Resources/get_channel_thumbnail.py"]}, "P0:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Release:CpResource /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/Resources/get_channel_videos.py /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Python/get_channel_videos.py": {"tool": "file-copy", "description": "CpResource /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/Resources/get_channel_videos.py /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Python/get_channel_videos.py", "inputs": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Python/get_channel_videos.py/", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--entry>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/Resources/get_channel_videos.py"]}, "P0:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Release:CpResource /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/Resources/install_dependencies.py /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Python/install_dependencies.py": {"tool": "file-copy", "description": "CpResource /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/Resources/install_dependencies.py /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Python/install_dependencies.py", "inputs": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Python/install_dependencies.py/", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--entry>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/Resources/install_dependencies.py"]}, "P0:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Release:CpResource /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/Resources/requirements.txt /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Python/requirements.txt": {"tool": "file-copy", "description": "CpResource /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/Resources/requirements.txt /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Python/requirements.txt", "inputs": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Python/requirements.txt/", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--entry>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/Resources/requirements.txt"]}, "P0:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Release:ExtractAppIntentsMetadata": {"tool": "shell", "description": "ExtractAppIntentsMetadata", "inputs": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/ContentView.swift", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Managers/DownloadManager.swift", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/YouTubeTranscriptDownloaderApp.swift", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloader-master.swiftconstvalues", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/MacOS/Scripture", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Scripture.DependencyMetadataFileList", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Scripture.DependencyStaticMetadataFileList", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture_dependency_info.dat", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.SwiftFileList", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.SwiftConstValuesFileList", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--entry>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata", "<ExtractAppIntentsMetadata /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/Resources/Metadata.appintents>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/appintentsmetadataprocessor", "--toolchain-dir", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain", "--module-name", "Scripture", "--sdk-root", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "--xcode-version", "16F6", "--platform-family", "macOS", "--deployment-target", "14.0", "--bundle-identifier", "com.yourcompany.YouTubeTranscriptDownloader", "--output", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/Resources", "--target-triple", "arm64-apple-macos14.0", "--binary-file", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/MacOS/Scripture", "--dependency-file", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture_dependency_info.dat", "--stringsdata-file", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata", "--source-file-list", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.SwiftFileList", "--metadata-file-list", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Scripture.DependencyMetadataFileList", "--static-metadata-file-list", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Scripture.DependencyStaticMetadataFileList", "--swift-const-vals-list", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.SwiftConstValuesFileList", "--compile-time-extraction", "--deployment-aware-processing", "--validate-assistant-intents", "--no-app-shortcuts-localization"], "env": {}, "working-directory": "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/YouTubeTranscriptDownloader", "signature": "dcf184a9f0d22ce0cbbd752cc9ee86f6"}, "P0:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Release:Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-Release-macosx--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation>", "<CreateBuildDirectory-/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath>", "<CreateBuildDirectory-/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath>", "<CreateBuildDirectory-/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release>", "<CreateBuildDirectory-/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release>"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>"]}, "P0:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Release:Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-linking": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-Release-macosx--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation>", "<CreateBuildDirectory-/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath>", "<CreateBuildDirectory-/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath>", "<CreateBuildDirectory-/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release>", "<CreateBuildDirectory-/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release>"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-linking>"]}, "P0:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Release:Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-scanning": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-Release-macosx--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation>", "<CreateBuildDirectory-/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath>", "<CreateBuildDirectory-/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath>", "<CreateBuildDirectory-/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release>", "<CreateBuildDirectory-/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-scanning>"]}, "P0:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Release:Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--end": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--entry>", "<CodeSign /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app>", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/assetcatalog_output/thinned/", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/assetcatalog_dependencies_thinned", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/assetcatalog_generated_info.plist_thinned", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/assetcatalog_output/unthinned/", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/assetcatalog_dependencies_unthinned", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/assetcatalog_generated_info.plist_unthinned", "<CopySwiftStdlib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app>", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/Resources/download_transcript.py", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/Resources/download_transcript_enhanced.py", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/Resources/get_channel_thumbnail.py", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/Resources/get_channel_videos.py", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/Resources/install_dependencies.py", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/Resources/requirements.txt", "<ExtractAppIntentsMetadata /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/Resources/Metadata.appintents>", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/DerivedSources/GeneratedAssetSymbols-Index.plist", "<GenerateDSYMFile /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/Scripture.app.dSYM/Contents/Resources/DWARF/Scripture>", "<GenerateDSYMFile /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/Scripture.app.dSYM/Contents/Resources/DWARF/Scripture>", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/assetcatalog_generated_info.plist", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/Resources/Assets.car", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/assetcatalog_signature", "<MkDir /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app>", "<MkDir /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents>", "<MkDir /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/MacOS>", "<MkDir /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/Resources>", "<MkDir /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/assetcatalog_output/thinned>", "<MkDir /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/assetcatalog_output/unthinned>", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/Info.plist", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/PkgInfo", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Scripture.app.xcent", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Scripture.app.xcent.der", "<RegisterExecutionPolicyException /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app>", "<LSRegisterURL /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app>", "<SetMode /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app>", "<SetOwner /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app>", "<Strip /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/MacOS/Scripture>", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloader Swift Compilation Finished", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/Scripture.app", "<Touch /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app>", "<Validate /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app>", "<ValidateDevelopmentAssets-/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build>", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/Scripture.swiftmodule/arm64-apple-macos.abi.json", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/Scripture.swiftmodule/arm64-apple-macos.swiftdoc", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/Scripture.swiftmodule/arm64-apple-macos.swiftmodule", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture_lto.o", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture_dependency_info.dat", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/DownloadManager.o", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloaderApp.o", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/DownloadManager.stringsdata", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloaderApp.stringsdata", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloader-master.swiftconstvalues", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.swiftmodule", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.swiftsourceinfo", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.abi.json", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture-Swift.h", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.swiftdoc", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/DerivedSources/Scripture-Swift.h", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/DerivedSources/Scripture-Swift.h", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/DerivedSources/Entitlements.plist", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.LinkFileList", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.SwiftConstValuesFileList", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.SwiftFileList", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloader-OutputFileMap.json", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloader_const_extract_protocols.json", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Scripture-all-non-framework-target-headers.hmap", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Scripture-all-target-headers.hmap", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Scripture-generated-files.hmap", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Scripture-own-target-headers.hmap", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Scripture-project-headers.hmap", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Scripture.DependencyMetadataFileList", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Scripture.DependencyStaticMetadataFileList", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Scripture.hmap", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/empty-Scripture.plist", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/Scripture.app.dSYM/", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--AppIntentsMetadataTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-ChangeAlternatePermissions>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-ChangePermissions>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-CodeSign>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-CopyAside>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-GenerateStubAPI>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-RegisterExecutionPolicyException>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-RegisterProduct>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-StripSymbols>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-Validate>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--CopySwiftPackageResourcesTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--CustomTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--DocumentationTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--GenerateAppPlaygroundAssetCatalogTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--GeneratedFilesTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--HeadermapTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--InfoPlistTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleMapTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ProductPostprocessingTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ProductStructureTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--RealityAssetsTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--SanitizerTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--StubBinaryTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--SwiftABIBaselineGenerationTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--SwiftFrameworkABICheckerTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--SwiftPackageCopyFilesTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--SwiftStandardLibrariesTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--TAPISymbolExtractorTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--TestHostTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--TestTargetPostprocessingTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--TestTargetTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--copy-headers-completion>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--generated-headers>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--swift-generated-headers>"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--end>"]}, "P0:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Release:Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--entry": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-Release-macosx--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation>", "<CreateBuildDirectory-/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath>", "<CreateBuildDirectory-/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath>", "<CreateBuildDirectory-/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release>", "<CreateBuildDirectory-/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--entry>"]}, "P0:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Release:Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--immediate": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-Release-macosx--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation>", "<CreateBuildDirectory-/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath>", "<CreateBuildDirectory-/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath>", "<CreateBuildDirectory-/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release>", "<CreateBuildDirectory-/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release>"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--immediate>"]}, "P0:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Release:Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--linker-inputs-ready": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture_lto.o", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture_dependency_info.dat", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/DownloadManager.o", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloaderApp.o", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/DownloadManager.stringsdata", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloaderApp.stringsdata", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloader-master.swiftconstvalues", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.swiftmodule", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.swiftsourceinfo", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.abi.json", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture-Swift.h", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.swiftdoc", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.LinkFileList"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--linker-inputs-ready>"]}, "P0:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Release:Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--modules-ready": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/Scripture.swiftmodule/arm64-apple-macos.abi.json", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/Scripture.swiftmodule/arm64-apple-macos.swiftdoc", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/Scripture.swiftmodule/arm64-apple-macos.swiftmodule", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/DownloadManager.o", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloaderApp.o", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/DownloadManager.stringsdata", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloaderApp.stringsdata", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloader-master.swiftconstvalues", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.swiftmodule", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.swiftsourceinfo", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.abi.json", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture-Swift.h", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.swiftdoc", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/DerivedSources/Scripture-Swift.h"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--modules-ready>"]}, "P0:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Release:Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--unsigned-product-ready": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/assetcatalog_output/thinned/", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/assetcatalog_dependencies_thinned", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/assetcatalog_generated_info.plist_thinned", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/assetcatalog_output/unthinned/", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/assetcatalog_dependencies_unthinned", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/assetcatalog_generated_info.plist_unthinned", "<CopySwiftStdlib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app>", "<ExtractAppIntentsMetadata /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/Resources/Metadata.appintents>", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/DerivedSources/GeneratedAssetSymbols-Index.plist", "<GenerateDSYMFile /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/Scripture.app.dSYM/Contents/Resources/DWARF/Scripture>", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/assetcatalog_generated_info.plist", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/Resources/Assets.car", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/assetcatalog_signature", "<MkDir /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/assetcatalog_output/thinned>", "<MkDir /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/assetcatalog_output/unthinned>", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Scripture.app.xcent", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Scripture.app.xcent.der", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloader Swift Compilation Finished", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/Scripture.swiftmodule/arm64-apple-macos.abi.json", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/Scripture.swiftmodule/arm64-apple-macos.swiftdoc", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/Scripture.swiftmodule/arm64-apple-macos.swiftmodule", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture_lto.o", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture_dependency_info.dat", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/DownloadManager.o", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloaderApp.o", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/DownloadManager.stringsdata", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloaderApp.stringsdata", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloader-master.swiftconstvalues", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.swiftmodule", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.swiftsourceinfo", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.abi.json", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture-Swift.h", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.swiftdoc", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/DerivedSources/Scripture-Swift.h", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/DerivedSources/Entitlements.plist", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.LinkFileList", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.SwiftConstValuesFileList", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.SwiftFileList", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloader-OutputFileMap.json", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloader_const_extract_protocols.json", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Scripture.DependencyMetadataFileList", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Scripture.DependencyStaticMetadataFileList", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-GenerateStubAPI>"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--unsigned-product-ready>"]}, "P0:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Release:Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--will-sign": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--unsigned-product-ready>"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--will-sign>"]}, "P0:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Release:GenerateAssetSymbols /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Assets.xcassets": {"tool": "shell", "description": "GenerateAssetSymbols /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Assets.xcassets", "inputs": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Assets.xcassets/", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/DerivedSources/GeneratedAssetSymbols-Index.plist"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/actool", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Assets.xcassets", "--compile", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/Resources", "--output-format", "human-readable-text", "--notices", "--warnings", "--export-dependency-info", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/assetcatalog_dependencies", "--output-partial-info-plist", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/assetcatalog_generated_info.plist", "--app-icon", "AppIcon", "--accent-color", "AccentColor", "--enable-on-demand-resources", "NO", "--development-region", "en", "--target-device", "mac", "--minimum-deployment-target", "14.0", "--platform", "macosx", "--bundle-identifier", "com.yourcompany.YouTubeTranscriptDownloader", "--generate-swift-asset-symbols", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/DerivedSources/GeneratedAssetSymbols.swift", "--generate-objc-asset-symbols", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/DerivedSources/GeneratedAssetSymbols.h", "--generate-asset-symbol-index", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/DerivedSources/GeneratedAssetSymbols-Index.plist"], "env": {}, "working-directory": "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/YouTubeTranscriptDownloader", "control-enabled": false, "signature": "d332c352bf83231703b89d55113489de"}, "P0:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Release:GenerateDSYMFile /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/Scripture.app.dSYM /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/MacOS/Scripture": {"tool": "shell", "description": "GenerateDSYMFile /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/Scripture.app.dSYM /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/MacOS/Scripture", "inputs": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/MacOS/Scripture", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/Info.plist", "<Linked Binary /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/MacOS/Scripture>", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.swiftmodule", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/Scripture.swiftmodule/arm64-apple-macos.swiftmodule", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/Scripture.app.dSYM/Contents/Resources/DWARF/Scripture", "<GenerateDSYMFile /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/Scripture.app.dSYM/Contents/Resources/DWARF/Scripture>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/dsymutil", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/MacOS/Scripture", "-o", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/Scripture.app.dSYM"], "env": {}, "working-directory": "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/YouTubeTranscriptDownloader", "signature": "69a9189e1d3145c40f8d2cb6dd62368d"}, "P0:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Release:LinkAssetCatalog /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Assets.xcassets": {"tool": "link-assetcatalog", "description": "LinkAssetCatalog /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Assets.xcassets", "inputs": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Assets.xcassets/", "<MkDir /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/Resources>", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/Resources", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/assetcatalog_output/thinned/", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/assetcatalog_output/unthinned/", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/assetcatalog_signature", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/assetcatalog_generated_info.plist", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/Resources/Assets.car"], "deps": "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/assetcatalog_dependencies"}, "P0:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Release:LinkAssetCatalogSignature": {"tool": "link-assetcatalog", "description": "LinkAssetCatalogSignature", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/assetcatalog_signature"], "always-out-of-date": true}, "P0:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Release:MkDir /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app": {"tool": "mkdir", "description": "MkDir /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--start>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--immediate>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app", "<MkDir /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app>", "<TRIGGER: MkDir /Users/<USER>/Downloads/Apps/Scripture/Scripture\\ 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app>"]}, "P0:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Release:MkDir /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents": {"tool": "mkdir", "description": "MkDir /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--start>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--immediate>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents", "<MkDir /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents>"]}, "P0:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Release:MkDir /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/MacOS": {"tool": "mkdir", "description": "MkDir /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/MacOS", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--start>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--immediate>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/MacOS", "<MkDir /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/MacOS>"]}, "P0:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Release:MkDir /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/Resources": {"tool": "mkdir", "description": "MkDir /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/Resources", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--start>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--immediate>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/Resources", "<MkDir /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/Resources>"]}, "P0:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Release:MkDir /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/assetcatalog_output/thinned": {"tool": "mkdir", "description": "MkDir /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/assetcatalog_output/thinned", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/assetcatalog_output/thinned", "<MkDir /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/assetcatalog_output/thinned>"]}, "P0:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Release:MkDir /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/assetcatalog_output/unthinned": {"tool": "mkdir", "description": "MkDir /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/assetcatalog_output/unthinned", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/assetcatalog_output/unthinned", "<MkDir /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/assetcatalog_output/unthinned>"]}, "P0:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Release:ProcessInfoPlistFile /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/Info.plist /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/empty-Scripture.plist": {"tool": "info-plist-processor", "description": "ProcessInfoPlistFile /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/Info.plist /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/empty-Scripture.plist", "inputs": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/empty-Scripture.plist", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/assetcatalog_generated_info.plist", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--entry>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/Info.plist", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/PkgInfo"]}, "P0:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Release:ProcessProductPackaging /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader.entitlements /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Scripture.app.xcent": {"tool": "process-product-entitlements", "description": "ProcessProductPackaging /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader.entitlements /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Scripture.app.xcent", "inputs": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader.entitlements", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/DerivedSources/Entitlements.plist", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ProductStructureTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--immediate>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Scripture.app.xcent"]}, "P0:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Release:ProcessProductPackagingDER /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Scripture.app.xcent /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Scripture.app.xcent.der": {"tool": "shell", "description": "ProcessProductPackagingDER /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Scripture.app.xcent /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Scripture.app.xcent.der", "inputs": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Scripture.app.xcent", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ProductStructureTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--immediate>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Scripture.app.xcent.der"], "args": ["/usr/bin/derq", "query", "-f", "xml", "-i", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Scripture.app.xcent", "-o", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Scripture.app.xcent.der", "--raw"], "env": {}, "working-directory": "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/YouTubeTranscriptDownloader", "signature": "ac948c1e37fdc363d606cb4803c0eefb"}, "P0:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Release:RegisterExecutionPolicyException /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app": {"tool": "register-execution-policy-exception", "description": "RegisterExecutionPolicyException /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app", "inputs": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-CodeSign>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--will-sign>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--entry>"], "outputs": ["<RegisterExecutionPolicyException /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app>"]}, "P0:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Release:RegisterWithLaunchServices /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app": {"tool": "lsregisterurl", "description": "RegisterWithLaunchServices /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-Validate>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--will-sign>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--entry>", "<TRIGGER: Validate /Users/<USER>/Downloads/Apps/Scripture/Scripture\\ 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app>"], "outputs": ["<LSRegisterURL /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app>"]}, "P0:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Release:SetMode u+w,go-w,a+rX /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app": {"tool": "shell", "description": "SetMode u+w,go-w,a+rX /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app", "inputs": ["<SetOwner /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-StripSymbols>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--will-sign>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--entry>", "<TRIGGER: SetOwnerAndGroup shrikant:staff /Users/<USER>/Downloads/Apps/Scripture/Scripture\\ 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app>"], "outputs": ["<SetMode /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app>", "<TRIGGER: SetMode u+w,go-w,a+rX /Users/<USER>/Downloads/Apps/Scripture/Scripture\\ 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app>"], "args": ["/bin/chmod", "-RH", "u+w,go-w,a+rX", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app"], "env": {}, "working-directory": "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/YouTubeTranscriptDownloader", "signature": "4ef1fe896b2bcbf1e5a70d7baca2ad7a"}, "P0:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Release:SetOwnerAndGroup shrikant:staff /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app": {"tool": "shell", "description": "SetOwnerAndGroup shrikant:staff /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-StripSymbols>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--will-sign>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--entry>", "<TRIGGER: MkDir /Users/<USER>/Downloads/Apps/Scripture/Scripture\\ 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app>"], "outputs": ["<SetOwner /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app>", "<TRIGGER: SetOwnerAndGroup shrikant:staff /Users/<USER>/Downloads/Apps/Scripture/Scripture\\ 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app>"], "args": ["/usr/sbin/chown", "-RH", "shrikant:staff", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app"], "env": {}, "working-directory": "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/YouTubeTranscriptDownloader", "signature": "fbb53bc79330ed1b5ff9a7dae6c69c12"}, "P0:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Release:Strip /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/MacOS/Scripture": {"tool": "shell", "description": "Strip /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/MacOS/Scripture", "inputs": ["<GenerateDSYMFile /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/Scripture.app.dSYM/Contents/Resources/DWARF/Scripture>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-CopyAside>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--will-sign>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--entry>", "<TRIGGER: Ld /Users/<USER>/Downloads/Apps/Scripture/Scripture\\ 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/MacOS/Scripture normal>"], "outputs": ["<Strip /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/MacOS/Scripture>", "<TRIGGER: Strip /Users/<USER>/Downloads/Apps/Scripture/Scripture\\ 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/MacOS/Scripture>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/strip", "-D", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/MacOS/Scripture"], "env": {}, "working-directory": "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/YouTubeTranscriptDownloader", "signature": "66916790a8db480504afec68232302c0"}, "P0:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Release:SwiftDriver Compilation YouTubeTranscriptDownloader normal arm64 com.apple.xcode.tools.swift.compiler": {"tool": "swift-driver-compilation", "description": "SwiftDriver Compilation YouTubeTranscriptDownloader normal arm64 com.apple.xcode.tools.swift.compiler", "inputs": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/ContentView.swift", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Managers/DownloadManager.swift", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/YouTubeTranscriptDownloaderApp.swift", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.SwiftFileList", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloader-OutputFileMap.json", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloader_const_extract_protocols.json", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Scripture-generated-files.hmap", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Scripture-own-target-headers.hmap", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Scripture-all-target-headers.hmap", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Scripture-project-headers.hmap", "<ClangStatCache /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--copy-headers-completion>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloader Swift Compilation Finished"]}, "P0:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Release:SymLink /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/Scripture.app ../../InstallationBuildProductsLocation/Applications/Scripture.app": {"tool": "symlink", "description": "SymLink /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/Scripture.app ../../InstallationBuildProductsLocation/Applications/Scripture.app", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--start>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--immediate>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/Scripture.app"], "contents": "../../InstallationBuildProductsLocation/Applications/Scripture.app", "repair-via-ownership-analysis": true}, "P0:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Release:Touch /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app": {"tool": "shell", "description": "Touch /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app", "inputs": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-Validate>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--will-sign>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--entry>"], "outputs": ["<Touch /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app>"], "args": ["/usr/bin/touch", "-c", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app"], "env": {}, "working-directory": "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/YouTubeTranscriptDownloader", "signature": "d7688a504b011db6f2468c60d73d0284"}, "P0:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Release:Validate /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app": {"tool": "validate-product", "description": "Validate /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app", "inputs": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/Info.plist", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-RegisterExecutionPolicyException>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--will-sign>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--entry>", "<TRIGGER: CodeSign /Users/<USER>/Downloads/Apps/Scripture/Scripture\\ 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app>"], "outputs": ["<Validate /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app>", "<TRIGGER: Validate /Users/<USER>/Downloads/Apps/Scripture/Scripture\\ 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app>"]}, "P0:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Release:ValidateDevelopmentAssets /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build": {"tool": "validate-development-assets", "description": "ValidateDevelopmentAssets /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build", "inputs": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Preview Content", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--entry>"], "outputs": ["<ValidateDevelopmentAssets-/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build>"], "allow-missing-inputs": true}, "P2:::WriteAuxiliaryFile /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60-VFS/all-product-headers.yaml": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60-VFS/all-product-headers.yaml", "inputs": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60-VFS/all-product-headers.yaml"]}, "P2:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Release:Copy /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/Scripture.swiftmodule/arm64-apple-macos.abi.json /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.abi.json": {"tool": "file-copy", "description": "Copy /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/Scripture.swiftmodule/arm64-apple-macos.abi.json /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.abi.json", "inputs": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.abi.json/", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--copy-headers-completion>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/Scripture.swiftmodule/arm64-apple-macos.abi.json"]}, "P2:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Release:Copy /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/Scripture.swiftmodule/arm64-apple-macos.swiftdoc /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.swiftdoc": {"tool": "file-copy", "description": "Copy /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/Scripture.swiftmodule/arm64-apple-macos.swiftdoc /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.swiftdoc", "inputs": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.swiftdoc/", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--copy-headers-completion>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/Scripture.swiftmodule/arm64-apple-macos.swiftdoc"]}, "P2:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Release:Copy /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/Scripture.swiftmodule/arm64-apple-macos.swiftmodule /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.swiftmodule": {"tool": "file-copy", "description": "Copy /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/Scripture.swiftmodule/arm64-apple-macos.swiftmodule /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.swiftmodule", "inputs": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.swiftmodule/", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--copy-headers-completion>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/Scripture.swiftmodule/arm64-apple-macos.swiftmodule"]}, "P2:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Release:Ld /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/MacOS/Scripture normal": {"tool": "shell", "description": "Ld /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/MacOS/Scripture normal", "inputs": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/DownloadManager.o", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloaderApp.o", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.LinkFileList", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--generated-headers>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--swift-generated-headers>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-linking>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/MacOS/Scripture", "<Linked Binary /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/MacOS/Scripture>", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture_lto.o", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture_dependency_info.dat", "<TRIGGER: Ld /Users/<USER>/Downloads/Apps/Scripture/Scripture\\ 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/MacOS/Scripture normal>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-<PERSON><PERSON><PERSON>", "-reproducible", "-target", "arm64-apple-macos14.0", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-<PERSON><PERSON>", "-L/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release", "-L/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release", "-F/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release", "-F/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release", "-filelist", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.LinkFileList", "-<PERSON><PERSON><PERSON>", "-rpath", "-<PERSON><PERSON><PERSON>", "@executable_path/../Frameworks", "-<PERSON><PERSON><PERSON>", "-object_path_lto", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture_lto.o", "-<PERSON><PERSON><PERSON>", "-final_output", "-<PERSON><PERSON><PERSON>", "/Applications/Scripture.app/Contents/MacOS/Scripture", "-<PERSON><PERSON><PERSON>", "-dependency_info", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture_dependency_info.dat", "-fobjc-link-runtime", "-L/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx", "-L/usr/lib/swift", "-<PERSON><PERSON><PERSON>", "-add_ast_path", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.swiftmodule", "-<PERSON><PERSON><PERSON>", "-no_adhoc_codesign", "-o", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/MacOS/Scripture"], "env": {}, "working-directory": "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/YouTubeTranscriptDownloader", "deps": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture_dependency_info.dat"], "deps-style": "dependency-info", "signature": "37c284bd71a499d4ffa99d3cd451f039"}, "P2:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Release:SwiftDriver Compilation Requirements YouTubeTranscriptDownloader normal arm64 com.apple.xcode.tools.swift.compiler": {"tool": "swift-driver-compilation-requirement", "description": "SwiftDriver Compilation Requirements YouTubeTranscriptDownloader normal arm64 com.apple.xcode.tools.swift.compiler", "inputs": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/ContentView.swift", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Managers/DownloadManager.swift", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/YouTubeTranscriptDownloaderApp.swift", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.SwiftFileList", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloader-OutputFileMap.json", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloader_const_extract_protocols.json", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Scripture-generated-files.hmap", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Scripture-own-target-headers.hmap", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Scripture-all-target-headers.hmap", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Scripture-project-headers.hmap", "<ClangStatCache /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--copy-headers-completion>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/DownloadManager.o", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloaderApp.o", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/DownloadManager.stringsdata", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloaderApp.stringsdata", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloader-master.swiftconstvalues", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.swiftmodule", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.swiftsourceinfo", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.abi.json", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture-Swift.h", "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.swiftdoc"]}, "P2:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Release:SwiftMergeGeneratedHeaders /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/DerivedSources/Scripture-Swift.h /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture-Swift.h": {"tool": "swift-header-tool", "description": "SwiftMergeGeneratedHeaders /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/DerivedSources/Scripture-Swift.h /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture-Swift.h", "inputs": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture-Swift.h", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/DerivedSources/Scripture-Swift.h"]}, "P2:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Release:WriteAuxiliaryFile /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/DerivedSources/Entitlements.plist": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/DerivedSources/Entitlements.plist", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--immediate>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/DerivedSources/Entitlements.plist"]}, "P2:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Release:WriteAuxiliaryFile /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.LinkFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.LinkFileList", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--immediate>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.LinkFileList"]}, "P2:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Release:WriteAuxiliaryFile /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.SwiftConstValuesFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.SwiftConstValuesFileList", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--immediate>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.SwiftConstValuesFileList"]}, "P2:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Release:WriteAuxiliaryFile /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.SwiftFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.SwiftFileList", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--immediate>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.SwiftFileList"]}, "P2:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Release:WriteAuxiliaryFile /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloader-OutputFileMap.json": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloader-OutputFileMap.json", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--immediate>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloader-OutputFileMap.json"]}, "P2:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Release:WriteAuxiliaryFile /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloader_const_extract_protocols.json": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloader_const_extract_protocols.json", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--immediate>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloader_const_extract_protocols.json"]}, "P2:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Release:WriteAuxiliaryFile /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Scripture-all-non-framework-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Scripture-all-non-framework-target-headers.hmap", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--immediate>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Scripture-all-non-framework-target-headers.hmap"]}, "P2:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Release:WriteAuxiliaryFile /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Scripture-all-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Scripture-all-target-headers.hmap", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--immediate>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Scripture-all-target-headers.hmap"]}, "P2:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Release:WriteAuxiliaryFile /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Scripture-generated-files.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Scripture-generated-files.hmap", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--immediate>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Scripture-generated-files.hmap"]}, "P2:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Release:WriteAuxiliaryFile /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Scripture-own-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Scripture-own-target-headers.hmap", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--immediate>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Scripture-own-target-headers.hmap"]}, "P2:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Release:WriteAuxiliaryFile /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Scripture-project-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Scripture-project-headers.hmap", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--immediate>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Scripture-project-headers.hmap"]}, "P2:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Release:WriteAuxiliaryFile /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Scripture.DependencyMetadataFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Scripture.DependencyMetadataFileList", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--immediate>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Scripture.DependencyMetadataFileList"]}, "P2:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Release:WriteAuxiliaryFile /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Scripture.DependencyStaticMetadataFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Scripture.DependencyStaticMetadataFileList", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--immediate>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Scripture.DependencyStaticMetadataFileList"]}, "P2:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Release:WriteAuxiliaryFile /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Scripture.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Scripture.hmap", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--immediate>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Scripture.hmap"]}, "P2:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Release:WriteAuxiliaryFile /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/empty-Scripture.plist": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/empty-Scripture.plist", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--immediate>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/empty-Scripture.plist"]}}}