{"buildCommand": {"command": "build", "skipDependencies": false, "style": "buildAndRun"}, "configuredTargets": [{"guid": "77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788"}], "containerPath": "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader.xcodeproj", "continueBuildingAfterErrors": false, "dependencyScope": "workspace", "enableIndexBuildArena": false, "hideShellScriptEnvironment": false, "parameters": {"action": "install", "activeArchitecture": "arm64", "activeRunDestination": {"disableOnlyActiveArch": false, "platform": "macosx", "sdk": "macosx15.5", "sdkVariant": "macos", "supportedArchitectures": ["arm64e", "arm64", "x86_64"], "targetArchitecture": "arm64"}, "arenaInfo": {"buildIntermediatesPath": "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex", "buildProductsPath": "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Products", "derivedDataPath": "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData", "indexDataStoreFolderPath": "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Index.noindex/DataStore", "indexEnableDataStore": true, "indexPCHPath": "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Index.noindex/PrecompiledHeaders", "pchPath": "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/PrecompiledHeaders"}, "configurationName": "Release", "overrides": {"commandLine": {"table": {}}, "synthesized": {"table": {"ACTION": "install", "ASSET_PACK_FOLDER_PATH": "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/OnDemandResources", "ASSETCATALOG_COMPILER_FLATTENED_APP_ICON_PATH": "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/ProductIcon.png", "DEPLOYMENT_LOCATION": "YES", "DEPLOYMENT_POSTPROCESSING": "YES", "DSTROOT": "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation", "EMBED_ASSET_PACKS_IN_PRODUCT_BUNDLE": "NO", "ENABLE_PREVIEWS": "NO", "ENABLE_SIGNATURE_AGGREGATION": "YES", "ENABLE_XOJIT_PREVIEWS": "YES", "INDEX_ENABLE_DATA_STORE": "NO", "MESSAGES_APPLICATION_EXTENSION_SUPPORT_FOLDER_PATH": "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/MessagesApplicationExtensionSupport", "MESSAGES_APPLICATION_SUPPORT_FOLDER_PATH": "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/MessagesApplicationSupport", "OBJROOT": "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath", "ONLY_ACTIVE_ARCH": "YES", "SHARED_PRECOMPS_DIR": "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/PrecompiledHeaders", "SIGNATURE_METADATA_FOLDER_PATH": "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Signatures", "SWIFT_STDLIB_TOOL_UNSIGNED_DESTINATION_DIR": "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/SwiftSupport", "SYMROOT": "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath", "WATCHKIT_2_SUPPORT_FOLDER_PATH": "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/WatchKitSupport2"}}}}, "schemeCommand": "archive", "showNonLoggedProgress": true, "useDryRun": false, "useImplicitDependencies": true, "useLegacyBuildLocations": false, "useParallelTargets": true}