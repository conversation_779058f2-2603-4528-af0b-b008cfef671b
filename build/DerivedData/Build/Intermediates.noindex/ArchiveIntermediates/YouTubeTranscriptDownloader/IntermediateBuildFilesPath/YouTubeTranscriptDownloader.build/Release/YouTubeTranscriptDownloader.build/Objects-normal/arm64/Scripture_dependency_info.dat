 @(#)PROGRAM:ld PROJECT:ld-1167.5
 /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Accessibility.framework/Accessibility.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/AppKit.framework/AppKit.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/ApplicationServices.framework/ApplicationServices.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ATS.framework/Versions/A/ATS.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ATS.framework/Versions/A/ATS.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ATSUI.framework/Versions/A/ATSUI.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ATSUI.framework/Versions/A/ATSUI.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ColorSyncLegacy.framework/Versions/A/ColorSyncLegacy.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ColorSyncLegacy.framework/Versions/A/ColorSyncLegacy.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/HIServices.framework/Versions/A/HIServices.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/HIServices.framework/Versions/A/HIServices.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/PrintCore.framework/Versions/A/PrintCore.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/PrintCore.framework/Versions/A/PrintCore.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/QD.framework/Versions/A/QD.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/QD.framework/Versions/A/QD.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/SpeechSynthesis.framework/Versions/A/SpeechSynthesis.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/SpeechSynthesis.framework/Versions/A/SpeechSynthesis.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CFNetwork.framework/CFNetwork.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/ColorSync.framework/ColorSync.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Combine.framework/Combine.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CoreData.framework/CoreData.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CoreFoundation.framework/CoreFoundation.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CoreGraphics.framework/CoreGraphics.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CoreImage.framework/CoreImage.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CoreServices.framework/CoreServices.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/AE.framework/Versions/A/AE.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/AE.framework/Versions/A/AE.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/CarbonCore.framework/Versions/A/CarbonCore.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/CarbonCore.framework/Versions/A/CarbonCore.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/DictionaryServices.framework/Versions/A/DictionaryServices.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/DictionaryServices.framework/Versions/A/DictionaryServices.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/FSEvents.framework/Versions/A/FSEvents.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/FSEvents.framework/Versions/A/FSEvents.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/LaunchServices.framework/Versions/A/LaunchServices.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/LaunchServices.framework/Versions/A/LaunchServices.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/Metadata.framework/Versions/A/Metadata.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/Metadata.framework/Versions/A/Metadata.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/OSServices.framework/Versions/A/OSServices.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/OSServices.framework/Versions/A/OSServices.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/SearchKit.framework/Versions/A/SearchKit.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/SearchKit.framework/Versions/A/SearchKit.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/SharedFileList.framework/Versions/A/SharedFileList.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/SharedFileList.framework/Versions/A/SharedFileList.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CoreText.framework/CoreText.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CoreTransferable.framework/CoreTransferable.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CoreVideo.framework/CoreVideo.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/DataDetection.framework/DataDetection.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/DeveloperToolsSupport.framework/DeveloperToolsSupport.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/DiskArbitration.framework/DiskArbitration.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Foundation.framework/Foundation.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/IOKit.framework/IOKit.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/IOSurface.framework/IOSurface.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/ImageIO.framework/ImageIO.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Metal.framework/Metal.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/OSLog.framework/OSLog.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/OpenGL.framework/OpenGL.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGL.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGL.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGLU.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGLU.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/QuartzCore.framework/QuartzCore.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Security.framework/Security.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/SwiftUI.framework/SwiftUI.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/SwiftUICore.framework/SwiftUICore.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/SwiftUICore.framework/Versions/A/SwiftUICore.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/SwiftUICore.framework/Versions/A/SwiftUICore.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Symbols.framework/Symbols.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/UniformTypeIdentifiers.framework/UniformTypeIdentifiers.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/PrivateFrameworks/CollectionViewCore.framework/Versions/A/CollectionViewCore.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/PrivateFrameworks/CollectionViewCore.framework/Versions/A/CollectionViewCore.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/PrivateFrameworks/UIFoundation.framework/Versions/A/UIFoundation.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/PrivateFrameworks/UIFoundation.framework/Versions/A/UIFoundation.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libSystem.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libcups.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libobjc.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswiftCore.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswiftCoreFoundation.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswiftCoreImage.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswiftDarwin.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswiftDataDetection.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswiftDispatch.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswiftFoundation.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswiftIOKit.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswiftMetal.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswiftOSLog.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswiftObjectiveC.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswiftObservation.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswiftQuartzCore.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswiftSpatial.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswiftSystem.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswiftUniformTypeIdentifiers.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswiftXPC.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswift_Builtin_float.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswift_Concurrency.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswift_StringProcessing.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswift_errno.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswift_math.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswift_signal.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswift_stdio.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswift_time.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswiftos.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswiftsimd.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswiftsys_time.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswiftunistd.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libcache.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libcache.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libcommonCrypto.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libcommonCrypto.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libcompiler_rt.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libcompiler_rt.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libcopyfile.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libcopyfile.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libcorecrypto.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libcorecrypto.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libdispatch.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libdispatch.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libdyld.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libdyld.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libkeymgr.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libkeymgr.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libmacho.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libmacho.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libquarantine.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libquarantine.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libremovefile.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libremovefile.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_asl.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_asl.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_blocks.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_blocks.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_c.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_c.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_collections.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_collections.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_configuration.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_configuration.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_containermanager.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_containermanager.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_coreservices.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_coreservices.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_darwin.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_darwin.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_darwindirectory.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_darwindirectory.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_dnssd.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_dnssd.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_eligibility.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_eligibility.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_featureflags.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_featureflags.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_info.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_info.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_kernel.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_kernel.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_m.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_m.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_malloc.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_malloc.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_networkextension.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_networkextension.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_notify.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_notify.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_platform.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_platform.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_pthread.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_pthread.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_sandbox.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_sandbox.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_sanitizers.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_sanitizers.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_secinit.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_secinit.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_symptoms.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_symptoms.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_trace.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_trace.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libunwind.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libunwind.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libxpc.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libxpc.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/lib/darwin/libclang_rt.osx.a /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/ContentView.o /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/DownloadManager.o /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/GeneratedAssetSymbols.o /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.LinkFileList /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.swiftmodule /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloaderApp.o /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/lib/darwin/libclang_rt.osx.a /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/lib/darwin/libclang_rt.osx.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/AE.framework/Versions/A/AE /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/AE.framework/Versions/A/AE.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/ATS.framework/Versions/A/ATS /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/ATS.framework/Versions/A/ATS.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/ATSUI.framework/Versions/A/ATSUI /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/ATSUI.framework/Versions/A/ATSUI.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Accessibility.framework/Accessibility /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/AppKit.framework/AppKit /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/ApplicationServices.framework/ApplicationServices /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ATS.framework/Versions/A/ATS /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ATSUI.framework/Versions/A/ATSUI /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ColorSyncLegacy.framework/Versions/A/ColorSyncLegacy /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/HIServices.framework/Versions/A/HIServices /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/PrintCore.framework/Versions/A/PrintCore /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/QD.framework/Versions/A/QD /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/SpeechSynthesis.framework/Versions/A/SpeechSynthesis /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CFNetwork.framework/CFNetwork /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CarbonCore.framework/Versions/A/CarbonCore /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CarbonCore.framework/Versions/A/CarbonCore.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CollectionViewCore.framework/Versions/A/CollectionViewCore /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CollectionViewCore.framework/Versions/A/CollectionViewCore.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/ColorSync.framework/ColorSync /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/ColorSyncLegacy.framework/Versions/A/ColorSyncLegacy /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/ColorSyncLegacy.framework/Versions/A/ColorSyncLegacy.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Combine.framework/Combine /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CoreData.framework/CoreData /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CoreFoundation.framework/CoreFoundation /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CoreGraphics.framework/CoreGraphics /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CoreImage.framework/CoreImage /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CoreServices.framework/CoreServices /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/AE.framework/Versions/A/AE /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/CarbonCore.framework/Versions/A/CarbonCore /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/DictionaryServices.framework/Versions/A/DictionaryServices /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/FSEvents.framework/Versions/A/FSEvents /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/LaunchServices.framework/Versions/A/LaunchServices /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/Metadata.framework/Versions/A/Metadata /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/OSServices.framework/Versions/A/OSServices /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/SearchKit.framework/Versions/A/SearchKit /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/SharedFileList.framework/Versions/A/SharedFileList /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CoreText.framework/CoreText /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CoreTransferable.framework/CoreTransferable /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CoreVideo.framework/CoreVideo /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/DataDetection.framework/DataDetection /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/DeveloperToolsSupport.framework/DeveloperToolsSupport /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/DictionaryServices.framework/Versions/A/DictionaryServices /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/DictionaryServices.framework/Versions/A/DictionaryServices.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/DiskArbitration.framework/DiskArbitration /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/FSEvents.framework/Versions/A/FSEvents /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/FSEvents.framework/Versions/A/FSEvents.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Foundation.framework/Foundation /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/HIServices.framework/Versions/A/HIServices /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/HIServices.framework/Versions/A/HIServices.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/IOKit.framework/IOKit /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/IOSurface.framework/IOSurface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/ImageIO.framework/ImageIO /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/LaunchServices.framework/Versions/A/LaunchServices /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/LaunchServices.framework/Versions/A/LaunchServices.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Metadata.framework/Versions/A/Metadata /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Metadata.framework/Versions/A/Metadata.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Metal.framework/Metal /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/OSLog.framework/OSLog /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/OSServices.framework/Versions/A/OSServices /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/OSServices.framework/Versions/A/OSServices.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/OpenGL.framework/OpenGL /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGL.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGLU.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/PrintCore.framework/Versions/A/PrintCore /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/PrintCore.framework/Versions/A/PrintCore.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/QD.framework/Versions/A/QD /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/QD.framework/Versions/A/QD.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/QuartzCore.framework/QuartzCore /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/SearchKit.framework/Versions/A/SearchKit /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/SearchKit.framework/Versions/A/SearchKit.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Security.framework/Security /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/SharedFileList.framework/Versions/A/SharedFileList /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/SharedFileList.framework/Versions/A/SharedFileList.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/SpeechSynthesis.framework/Versions/A/SpeechSynthesis /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/SpeechSynthesis.framework/Versions/A/SpeechSynthesis.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/SwiftUI.framework/SwiftUI /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/SwiftUICore.framework/SwiftUICore /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/SwiftUICore.framework/Versions/A/SwiftUICore /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Symbols.framework/Symbols /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/UIFoundation.framework/Versions/A/UIFoundation /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/UIFoundation.framework/Versions/A/UIFoundation.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/UniformTypeIdentifiers.framework/UniformTypeIdentifiers /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/PrivateFrameworks/CollectionViewCore.framework/Versions/A/CollectionViewCore /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/PrivateFrameworks/UIFoundation.framework/Versions/A/UIFoundation /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libSystem.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libcache.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libcache.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libcommonCrypto.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libcommonCrypto.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libcompiler_rt.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libcompiler_rt.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libcopyfile.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libcopyfile.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libcorecrypto.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libcorecrypto.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libcups.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libdispatch.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libdispatch.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libdyld.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libdyld.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libkeymgr.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libkeymgr.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libmacho.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libmacho.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libobjc.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libquarantine.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libquarantine.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libremovefile.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libremovefile.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_asl.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_asl.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_blocks.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_blocks.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_c.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_c.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_collections.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_collections.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_configuration.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_configuration.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_containermanager.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_containermanager.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_coreservices.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_coreservices.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_darwin.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_darwin.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_darwindirectory.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_darwindirectory.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_dnssd.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_dnssd.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_eligibility.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_eligibility.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_featureflags.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_featureflags.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_info.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_info.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_kernel.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_kernel.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_m.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_m.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_malloc.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_malloc.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_networkextension.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_networkextension.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_notify.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_notify.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_platform.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_platform.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_pthread.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_pthread.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_sandbox.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_sandbox.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_sanitizers.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_sanitizers.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_secinit.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_secinit.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_symptoms.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_symptoms.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_trace.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_trace.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libunwind.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libunwind.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libxpc.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libxpc.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libSystem.a /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libSystem.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libSystem.so /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libSystem.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libcache.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libcache.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libcommonCrypto.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libcommonCrypto.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libcompiler_rt.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libcompiler_rt.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libcopyfile.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libcopyfile.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libcorecrypto.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libcorecrypto.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libcups.a /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libcups.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libcups.so /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libcups.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libdispatch.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libdispatch.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libdyld.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libdyld.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libkeymgr.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libkeymgr.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libmacho.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libmacho.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libobjc.a /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libobjc.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libobjc.so /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libobjc.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libquarantine.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libquarantine.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libremovefile.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libremovefile.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswiftCore.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswiftCoreFoundation.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswiftCoreImage.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswiftDarwin.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswiftDataDetection.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswiftDispatch.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswiftFoundation.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswiftIOKit.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswiftMetal.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswiftOSLog.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswiftObjectiveC.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswiftObservation.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswiftQuartzCore.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswiftSpatial.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswiftSystem.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswiftUniformTypeIdentifiers.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswiftXPC.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswift_Builtin_float.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswift_Concurrency.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswift_StringProcessing.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswift_errno.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswift_math.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswift_signal.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswift_stdio.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswift_time.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswiftos.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswiftsimd.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswiftsys_time.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswiftunistd.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_asl.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_asl.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_blocks.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_blocks.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_c.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_c.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_collections.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_collections.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_configuration.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_configuration.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_containermanager.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_containermanager.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_coreservices.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_coreservices.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_darwin.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_darwin.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_darwindirectory.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_darwindirectory.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_dnssd.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_dnssd.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_eligibility.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_eligibility.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_featureflags.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_featureflags.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_info.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_info.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_kernel.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_kernel.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_m.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_m.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_malloc.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_malloc.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_networkextension.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_networkextension.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_notify.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_notify.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_platform.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_platform.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_pthread.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_pthread.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_sandbox.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_sandbox.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_sanitizers.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_sanitizers.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_secinit.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_secinit.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_symptoms.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_symptoms.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_trace.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_trace.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libunwind.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libunwind.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libxpc.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libxpc.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libcache.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libcommonCrypto.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libcompiler_rt.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libcopyfile.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libcorecrypto.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libdispatch.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libdyld.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libkeymgr.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libmacho.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libquarantine.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libremovefile.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_asl.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_blocks.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_c.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_collections.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_configuration.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_containermanager.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_coreservices.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_darwin.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_darwindirectory.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_dnssd.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_eligibility.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_featureflags.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_info.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_kernel.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_m.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_malloc.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_networkextension.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_notify.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_platform.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_pthread.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_sandbox.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_sanitizers.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_secinit.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_symptoms.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_trace.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libunwind.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libxpc.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libSystem.a /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libSystem.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libSystem.so /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libSystem.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libcache.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libcache.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libcommonCrypto.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libcommonCrypto.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libcompiler_rt.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libcompiler_rt.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libcopyfile.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libcopyfile.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libcorecrypto.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libcorecrypto.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libcups.a /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libcups.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libcups.so /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libcups.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libdispatch.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libdispatch.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libdyld.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libdyld.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libkeymgr.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libkeymgr.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libmacho.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libmacho.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libobjc.a /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libobjc.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libobjc.so /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libobjc.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libquarantine.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libquarantine.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libremovefile.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libremovefile.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftCore.a /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftCore.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftCore.so /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftCore.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftCoreFoundation.a /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftCoreFoundation.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftCoreFoundation.so /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftCoreFoundation.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftCoreImage.a /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftCoreImage.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftCoreImage.so /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftCoreImage.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftDarwin.a /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftDarwin.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftDarwin.so /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftDarwin.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftDataDetection.a /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftDataDetection.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftDataDetection.so /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftDataDetection.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftDispatch.a /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftDispatch.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftDispatch.so /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftDispatch.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftFoundation.a /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftFoundation.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftFoundation.so /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftFoundation.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftIOKit.a /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftIOKit.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftIOKit.so /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftIOKit.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftMetal.a /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftMetal.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftMetal.so /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftMetal.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftOSLog.a /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftOSLog.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftOSLog.so /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftOSLog.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftObjectiveC.a /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftObjectiveC.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftObjectiveC.so /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftObjectiveC.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftObservation.a /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftObservation.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftObservation.so /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftObservation.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftQuartzCore.a /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftQuartzCore.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftQuartzCore.so /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftQuartzCore.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftSpatial.a /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftSpatial.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftSpatial.so /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftSpatial.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftSystem.a /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftSystem.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftSystem.so /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftSystem.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftUniformTypeIdentifiers.a /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftUniformTypeIdentifiers.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftUniformTypeIdentifiers.so /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftUniformTypeIdentifiers.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftXPC.a /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftXPC.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftXPC.so /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftXPC.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswift_Builtin_float.a /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswift_Builtin_float.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswift_Builtin_float.so /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswift_Builtin_float.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswift_Concurrency.a /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswift_Concurrency.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswift_Concurrency.so /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswift_Concurrency.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswift_StringProcessing.a /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswift_StringProcessing.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswift_StringProcessing.so /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswift_StringProcessing.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswift_errno.a /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswift_errno.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswift_errno.so /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswift_errno.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswift_math.a /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswift_math.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswift_math.so /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswift_math.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswift_signal.a /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswift_signal.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswift_signal.so /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswift_signal.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswift_stdio.a /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswift_stdio.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswift_stdio.so /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswift_stdio.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswift_time.a /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswift_time.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswift_time.so /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswift_time.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftos.a /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftos.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftos.so /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftos.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftsimd.a /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftsimd.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftsimd.so /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftsimd.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftsys_time.a /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftsys_time.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftsys_time.so /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftsys_time.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftunistd.a /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftunistd.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftunistd.so /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftunistd.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_asl.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_asl.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_blocks.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_blocks.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_c.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_c.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_collections.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_collections.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_configuration.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_configuration.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_containermanager.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_containermanager.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_coreservices.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_coreservices.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_darwin.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_darwin.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_darwindirectory.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_darwindirectory.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_dnssd.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_dnssd.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_eligibility.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_eligibility.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_featureflags.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_featureflags.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_info.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_info.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_kernel.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_kernel.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_m.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_m.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_malloc.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_malloc.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_networkextension.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_networkextension.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_notify.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_notify.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_platform.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_platform.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_pthread.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_pthread.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_sandbox.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_sandbox.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_sanitizers.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_sanitizers.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_secinit.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_secinit.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_symptoms.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_symptoms.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_trace.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_trace.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libunwind.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libunwind.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libxpc.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libxpc.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/AE.framework/Versions/A/AE /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/AE.framework/Versions/A/AE.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/ATS.framework/Versions/A/ATS /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/ATS.framework/Versions/A/ATS.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/ATSUI.framework/Versions/A/ATSUI /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/ATSUI.framework/Versions/A/ATSUI.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/Accessibility.framework/Accessibility /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/Accessibility.framework/Accessibility.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/AppKit.framework/AppKit /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/AppKit.framework/AppKit.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/ApplicationServices.framework/ApplicationServices /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/ApplicationServices.framework/ApplicationServices.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/CFNetwork.framework/CFNetwork /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/CFNetwork.framework/CFNetwork.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/CarbonCore.framework/Versions/A/CarbonCore /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/CarbonCore.framework/Versions/A/CarbonCore.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/CollectionViewCore.framework/Versions/A/CollectionViewCore /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/CollectionViewCore.framework/Versions/A/CollectionViewCore.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/ColorSync.framework/ColorSync /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/ColorSync.framework/ColorSync.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/ColorSyncLegacy.framework/Versions/A/ColorSyncLegacy /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/ColorSyncLegacy.framework/Versions/A/ColorSyncLegacy.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/Combine.framework/Combine /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/Combine.framework/Combine.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/CoreData.framework/CoreData /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/CoreData.framework/CoreData.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/CoreFoundation.framework/CoreFoundation /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/CoreFoundation.framework/CoreFoundation.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/CoreGraphics.framework/CoreGraphics /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/CoreGraphics.framework/CoreGraphics.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/CoreImage.framework/CoreImage /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/CoreImage.framework/CoreImage.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/CoreServices.framework/CoreServices /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/CoreServices.framework/CoreServices.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/CoreText.framework/CoreText /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/CoreText.framework/CoreText.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/CoreTransferable.framework/CoreTransferable /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/CoreTransferable.framework/CoreTransferable.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/CoreVideo.framework/CoreVideo /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/CoreVideo.framework/CoreVideo.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/DataDetection.framework/DataDetection /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/DataDetection.framework/DataDetection.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/DeveloperToolsSupport.framework/DeveloperToolsSupport /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/DeveloperToolsSupport.framework/DeveloperToolsSupport.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/DictionaryServices.framework/Versions/A/DictionaryServices /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/DictionaryServices.framework/Versions/A/DictionaryServices.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/DiskArbitration.framework/DiskArbitration /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/DiskArbitration.framework/DiskArbitration.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/FSEvents.framework/Versions/A/FSEvents /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/FSEvents.framework/Versions/A/FSEvents.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/Foundation.framework/Foundation /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/Foundation.framework/Foundation.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/HIServices.framework/Versions/A/HIServices /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/HIServices.framework/Versions/A/HIServices.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/IOKit.framework/IOKit /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/IOKit.framework/IOKit.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/IOSurface.framework/IOSurface /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/IOSurface.framework/IOSurface.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/ImageIO.framework/ImageIO /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/ImageIO.framework/ImageIO.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/LaunchServices.framework/Versions/A/LaunchServices /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/LaunchServices.framework/Versions/A/LaunchServices.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/Metadata.framework/Versions/A/Metadata /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/Metadata.framework/Versions/A/Metadata.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/Metal.framework/Metal /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/Metal.framework/Metal.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/OSLog.framework/OSLog /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/OSLog.framework/OSLog.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/OSServices.framework/Versions/A/OSServices /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/OSServices.framework/Versions/A/OSServices.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/OpenGL.framework/OpenGL /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/OpenGL.framework/OpenGL.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/PrintCore.framework/Versions/A/PrintCore /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/PrintCore.framework/Versions/A/PrintCore.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/QD.framework/Versions/A/QD /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/QD.framework/Versions/A/QD.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/QuartzCore.framework/QuartzCore /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/QuartzCore.framework/QuartzCore.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/SearchKit.framework/Versions/A/SearchKit /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/SearchKit.framework/Versions/A/SearchKit.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/Security.framework/Security /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/Security.framework/Security.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/SharedFileList.framework/Versions/A/SharedFileList /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/SharedFileList.framework/Versions/A/SharedFileList.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/SpeechSynthesis.framework/Versions/A/SpeechSynthesis /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/SpeechSynthesis.framework/Versions/A/SpeechSynthesis.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/SwiftUI.framework/SwiftUI /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/SwiftUI.framework/SwiftUI.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/SwiftUICore.framework/SwiftUICore /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/SwiftUICore.framework/SwiftUICore.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/SwiftUICore.framework/Versions/A/SwiftUICore /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/SwiftUICore.framework/Versions/A/SwiftUICore.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/Symbols.framework/Symbols /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/Symbols.framework/Symbols.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/UIFoundation.framework/Versions/A/UIFoundation /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/UIFoundation.framework/Versions/A/UIFoundation.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/UniformTypeIdentifiers.framework/UniformTypeIdentifiers /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/UniformTypeIdentifiers.framework/UniformTypeIdentifiers.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libSystem.a /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libSystem.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libSystem.so /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libSystem.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libcache.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libcache.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libcommonCrypto.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libcommonCrypto.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libcompiler_rt.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libcompiler_rt.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libcopyfile.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libcopyfile.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libcorecrypto.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libcorecrypto.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libcups.a /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libcups.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libcups.so /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libcups.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libdispatch.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libdispatch.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libdyld.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libdyld.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libkeymgr.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libkeymgr.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libmacho.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libmacho.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libobjc.a /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libobjc.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libobjc.so /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libobjc.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libquarantine.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libquarantine.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libremovefile.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libremovefile.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswiftCore.a /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswiftCore.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswiftCore.so /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswiftCore.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswiftCoreFoundation.a /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswiftCoreFoundation.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswiftCoreFoundation.so /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswiftCoreFoundation.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswiftCoreImage.a /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswiftCoreImage.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswiftCoreImage.so /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswiftCoreImage.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswiftDarwin.a /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswiftDarwin.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswiftDarwin.so /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswiftDarwin.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswiftDataDetection.a /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswiftDataDetection.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswiftDataDetection.so /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswiftDataDetection.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswiftDispatch.a /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswiftDispatch.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswiftDispatch.so /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswiftDispatch.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswiftFoundation.a /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswiftFoundation.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswiftFoundation.so /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswiftFoundation.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswiftIOKit.a /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswiftIOKit.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswiftIOKit.so /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswiftIOKit.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswiftMetal.a /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswiftMetal.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswiftMetal.so /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswiftMetal.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswiftOSLog.a /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswiftOSLog.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswiftOSLog.so /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswiftOSLog.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswiftObjectiveC.a /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswiftObjectiveC.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswiftObjectiveC.so /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswiftObjectiveC.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswiftObservation.a /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswiftObservation.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswiftObservation.so /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswiftObservation.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswiftQuartzCore.a /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswiftQuartzCore.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswiftQuartzCore.so /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswiftQuartzCore.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswiftSpatial.a /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswiftSpatial.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswiftSpatial.so /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswiftSpatial.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswiftSystem.a /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswiftSystem.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswiftSystem.so /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswiftSystem.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswiftUniformTypeIdentifiers.a /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswiftUniformTypeIdentifiers.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswiftUniformTypeIdentifiers.so /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswiftUniformTypeIdentifiers.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswiftXPC.a /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswiftXPC.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswiftXPC.so /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswiftXPC.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswift_Builtin_float.a /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswift_Builtin_float.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswift_Builtin_float.so /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswift_Builtin_float.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswift_Concurrency.a /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswift_Concurrency.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswift_Concurrency.so /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswift_Concurrency.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswift_StringProcessing.a /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswift_StringProcessing.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswift_StringProcessing.so /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswift_StringProcessing.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswift_errno.a /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswift_errno.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswift_errno.so /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswift_errno.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswift_math.a /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswift_math.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswift_math.so /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswift_math.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswift_signal.a /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswift_signal.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswift_signal.so /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswift_signal.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswift_stdio.a /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswift_stdio.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswift_stdio.so /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswift_stdio.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswift_time.a /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswift_time.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswift_time.so /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswift_time.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswiftos.a /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswiftos.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswiftos.so /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswiftos.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswiftsimd.a /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswiftsimd.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswiftsimd.so /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswiftsimd.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswiftsys_time.a /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswiftsys_time.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswiftsys_time.so /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswiftsys_time.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswiftunistd.a /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswiftunistd.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswiftunistd.so /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libswiftunistd.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libsystem_asl.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libsystem_asl.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libsystem_blocks.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libsystem_blocks.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libsystem_c.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libsystem_c.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libsystem_collections.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libsystem_collections.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libsystem_configuration.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libsystem_configuration.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libsystem_containermanager.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libsystem_containermanager.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libsystem_coreservices.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libsystem_coreservices.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libsystem_darwin.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libsystem_darwin.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libsystem_darwindirectory.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libsystem_darwindirectory.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libsystem_dnssd.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libsystem_dnssd.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libsystem_eligibility.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libsystem_eligibility.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libsystem_featureflags.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libsystem_featureflags.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libsystem_info.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libsystem_info.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libsystem_kernel.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libsystem_kernel.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libsystem_m.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libsystem_m.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libsystem_malloc.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libsystem_malloc.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libsystem_networkextension.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libsystem_networkextension.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libsystem_notify.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libsystem_notify.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libsystem_platform.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libsystem_platform.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libsystem_pthread.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libsystem_pthread.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libsystem_sandbox.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libsystem_sandbox.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libsystem_sanitizers.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libsystem_sanitizers.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libsystem_secinit.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libsystem_secinit.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libsystem_symptoms.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libsystem_symptoms.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libsystem_trace.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libsystem_trace.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libunwind.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libunwind.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libxpc.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/BuildProductsPath/Release/libxpc.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/AE.framework/Versions/A/AE /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/AE.framework/Versions/A/AE.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/ATS.framework/Versions/A/ATS /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/ATS.framework/Versions/A/ATS.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/ATSUI.framework/Versions/A/ATSUI /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/ATSUI.framework/Versions/A/ATSUI.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/Accessibility.framework/Accessibility /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/Accessibility.framework/Accessibility.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/AppKit.framework/AppKit /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/AppKit.framework/AppKit.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/ApplicationServices.framework/ApplicationServices /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/ApplicationServices.framework/ApplicationServices.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/CFNetwork.framework/CFNetwork /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/CFNetwork.framework/CFNetwork.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/CarbonCore.framework/Versions/A/CarbonCore /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/CarbonCore.framework/Versions/A/CarbonCore.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/CollectionViewCore.framework/Versions/A/CollectionViewCore /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/CollectionViewCore.framework/Versions/A/CollectionViewCore.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/ColorSync.framework/ColorSync /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/ColorSync.framework/ColorSync.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/ColorSyncLegacy.framework/Versions/A/ColorSyncLegacy /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/ColorSyncLegacy.framework/Versions/A/ColorSyncLegacy.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/Combine.framework/Combine /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/Combine.framework/Combine.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/CoreData.framework/CoreData /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/CoreData.framework/CoreData.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/CoreFoundation.framework/CoreFoundation /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/CoreFoundation.framework/CoreFoundation.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/CoreGraphics.framework/CoreGraphics /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/CoreGraphics.framework/CoreGraphics.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/CoreImage.framework/CoreImage /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/CoreImage.framework/CoreImage.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/CoreServices.framework/CoreServices /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/CoreServices.framework/CoreServices.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/CoreText.framework/CoreText /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/CoreText.framework/CoreText.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/CoreTransferable.framework/CoreTransferable /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/CoreTransferable.framework/CoreTransferable.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/CoreVideo.framework/CoreVideo /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/CoreVideo.framework/CoreVideo.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/DataDetection.framework/DataDetection /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/DataDetection.framework/DataDetection.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/DeveloperToolsSupport.framework/DeveloperToolsSupport /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/DeveloperToolsSupport.framework/DeveloperToolsSupport.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/DictionaryServices.framework/Versions/A/DictionaryServices /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/DictionaryServices.framework/Versions/A/DictionaryServices.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/DiskArbitration.framework/DiskArbitration /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/DiskArbitration.framework/DiskArbitration.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/FSEvents.framework/Versions/A/FSEvents /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/FSEvents.framework/Versions/A/FSEvents.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/Foundation.framework/Foundation /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/Foundation.framework/Foundation.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/HIServices.framework/Versions/A/HIServices /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/HIServices.framework/Versions/A/HIServices.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/IOKit.framework/IOKit /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/IOKit.framework/IOKit.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/IOSurface.framework/IOSurface /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/IOSurface.framework/IOSurface.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/ImageIO.framework/ImageIO /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/ImageIO.framework/ImageIO.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/LaunchServices.framework/Versions/A/LaunchServices /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/LaunchServices.framework/Versions/A/LaunchServices.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/Metadata.framework/Versions/A/Metadata /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/Metadata.framework/Versions/A/Metadata.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/Metal.framework/Metal /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/Metal.framework/Metal.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/OSLog.framework/OSLog /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/OSLog.framework/OSLog.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/OSServices.framework/Versions/A/OSServices /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/OSServices.framework/Versions/A/OSServices.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/OpenGL.framework/OpenGL /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/OpenGL.framework/OpenGL.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/PrintCore.framework/Versions/A/PrintCore /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/PrintCore.framework/Versions/A/PrintCore.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/QD.framework/Versions/A/QD /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/QD.framework/Versions/A/QD.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/QuartzCore.framework/QuartzCore /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/QuartzCore.framework/QuartzCore.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/SearchKit.framework/Versions/A/SearchKit /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/SearchKit.framework/Versions/A/SearchKit.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/Security.framework/Security /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/Security.framework/Security.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/SharedFileList.framework/Versions/A/SharedFileList /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/SharedFileList.framework/Versions/A/SharedFileList.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/SpeechSynthesis.framework/Versions/A/SpeechSynthesis /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/SpeechSynthesis.framework/Versions/A/SpeechSynthesis.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/SwiftUI.framework/SwiftUI /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/SwiftUI.framework/SwiftUI.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/SwiftUICore.framework/SwiftUICore /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/SwiftUICore.framework/SwiftUICore.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/SwiftUICore.framework/Versions/A/SwiftUICore /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/SwiftUICore.framework/Versions/A/SwiftUICore.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/Symbols.framework/Symbols /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/Symbols.framework/Symbols.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/UIFoundation.framework/Versions/A/UIFoundation /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/UIFoundation.framework/Versions/A/UIFoundation.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/UniformTypeIdentifiers.framework/UniformTypeIdentifiers /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/UniformTypeIdentifiers.framework/UniformTypeIdentifiers.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libSystem.a /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libSystem.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libSystem.so /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libSystem.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libcache.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libcache.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libcommonCrypto.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libcommonCrypto.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libcompiler_rt.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libcompiler_rt.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libcopyfile.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libcopyfile.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libcorecrypto.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libcorecrypto.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libcups.a /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libcups.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libcups.so /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libcups.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libdispatch.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libdispatch.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libdyld.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libdyld.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libkeymgr.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libkeymgr.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libmacho.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libmacho.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libobjc.a /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libobjc.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libobjc.so /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libobjc.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libquarantine.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libquarantine.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libremovefile.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libremovefile.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswiftCore.a /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswiftCore.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswiftCore.so /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswiftCore.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswiftCoreFoundation.a /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswiftCoreFoundation.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswiftCoreFoundation.so /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswiftCoreFoundation.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswiftCoreImage.a /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswiftCoreImage.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswiftCoreImage.so /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswiftCoreImage.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswiftDarwin.a /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswiftDarwin.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswiftDarwin.so /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswiftDarwin.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswiftDataDetection.a /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswiftDataDetection.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswiftDataDetection.so /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswiftDataDetection.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswiftDispatch.a /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswiftDispatch.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswiftDispatch.so /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswiftDispatch.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswiftFoundation.a /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswiftFoundation.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswiftFoundation.so /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswiftFoundation.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswiftIOKit.a /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswiftIOKit.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswiftIOKit.so /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswiftIOKit.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswiftMetal.a /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswiftMetal.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswiftMetal.so /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswiftMetal.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswiftOSLog.a /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswiftOSLog.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswiftOSLog.so /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswiftOSLog.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswiftObjectiveC.a /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswiftObjectiveC.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswiftObjectiveC.so /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswiftObjectiveC.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswiftObservation.a /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswiftObservation.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswiftObservation.so /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswiftObservation.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswiftQuartzCore.a /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswiftQuartzCore.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswiftQuartzCore.so /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswiftQuartzCore.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswiftSpatial.a /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswiftSpatial.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswiftSpatial.so /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswiftSpatial.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswiftSystem.a /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswiftSystem.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswiftSystem.so /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswiftSystem.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswiftUniformTypeIdentifiers.a /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswiftUniformTypeIdentifiers.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswiftUniformTypeIdentifiers.so /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswiftUniformTypeIdentifiers.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswiftXPC.a /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswiftXPC.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswiftXPC.so /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswiftXPC.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswift_Builtin_float.a /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswift_Builtin_float.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswift_Builtin_float.so /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswift_Builtin_float.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswift_Concurrency.a /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswift_Concurrency.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswift_Concurrency.so /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswift_Concurrency.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswift_StringProcessing.a /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswift_StringProcessing.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswift_StringProcessing.so /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswift_StringProcessing.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswift_errno.a /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswift_errno.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswift_errno.so /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswift_errno.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswift_math.a /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswift_math.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswift_math.so /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswift_math.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswift_signal.a /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswift_signal.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswift_signal.so /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswift_signal.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswift_stdio.a /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswift_stdio.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswift_stdio.so /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswift_stdio.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswift_time.a /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswift_time.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswift_time.so /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswift_time.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswiftos.a /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswiftos.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswiftos.so /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswiftos.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswiftsimd.a /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswiftsimd.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswiftsimd.so /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswiftsimd.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswiftsys_time.a /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswiftsys_time.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswiftsys_time.so /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswiftsys_time.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswiftunistd.a /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswiftunistd.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswiftunistd.so /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libswiftunistd.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libsystem_asl.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libsystem_asl.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libsystem_blocks.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libsystem_blocks.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libsystem_c.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libsystem_c.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libsystem_collections.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libsystem_collections.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libsystem_configuration.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libsystem_configuration.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libsystem_containermanager.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libsystem_containermanager.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libsystem_coreservices.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libsystem_coreservices.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libsystem_darwin.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libsystem_darwin.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libsystem_darwindirectory.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libsystem_darwindirectory.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libsystem_dnssd.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libsystem_dnssd.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libsystem_eligibility.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libsystem_eligibility.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libsystem_featureflags.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libsystem_featureflags.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libsystem_info.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libsystem_info.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libsystem_kernel.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libsystem_kernel.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libsystem_m.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libsystem_m.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libsystem_malloc.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libsystem_malloc.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libsystem_networkextension.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libsystem_networkextension.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libsystem_notify.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libsystem_notify.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libsystem_platform.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libsystem_platform.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libsystem_pthread.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libsystem_pthread.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libsystem_sandbox.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libsystem_sandbox.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libsystem_sanitizers.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libsystem_sanitizers.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libsystem_secinit.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libsystem_secinit.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libsystem_symptoms.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libsystem_symptoms.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libsystem_trace.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libsystem_trace.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libunwind.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libunwind.tbd /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libxpc.dylib /Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/EagerLinkingTBDs/Release/libxpc.tbd @/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/InstallationBuildProductsLocation/Applications/Scripture.app/Contents/MacOS/Scripture 