"/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/ContentView.swift":
  diagnostics: "/Users/<USER>/Downloads/Apps/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloader-master.dia"
  swiftdoc: "/Users/<USER>/Downloads/Apps/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.swiftdoc"
  swiftmodule: "/Users/<USER>/Downloads/Apps/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.swiftmodule"
  objc-header: "/Users/<USER>/Downloads/Apps/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture-Swift.h"
  dependencies: "/Users/<USER>/Downloads/Apps/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloader-master.d"
  swiftsourceinfo: "/Users/<USER>/Downloads/Apps/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.swiftsourceinfo"
  abi-baseline-json: "/Users/<USER>/Downloads/Apps/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.abi.json"
  const-values: "/Users/<USER>/Downloads/Apps/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloader-master.swiftconstvalues"
