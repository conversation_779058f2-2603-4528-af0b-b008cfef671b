version: "Apple Swift version 6.1.2 (swiftlang-*******.2 clang-1700.0.13.5)"
options: "305830e396ed3d1059a721faaf60f538c54f0616e61a9c43d4d043b4e7f53787"
build_start_time: [1754044311, 325895000]
build_end_time: [1754044322, 141130000]
inputs:
  "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/ContentView.swift": [1754043762, 254862179]
  ? "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Managers/DownloadManager.swift"
  : [1754044076, 616109532]
  ? "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/YouTubeTranscriptDownloaderApp.swift"
  : [1753870596, 175367951]
  ? "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/DerivedSources/GeneratedAssetSymbols.swift"
  : [1754044310, 2814777]
