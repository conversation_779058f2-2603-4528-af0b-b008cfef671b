version: "Apple Swift version 6.1.2 (swiftlang-*******.2 clang-1700.0.13.5)"
options: "61383eb805bb6bb3eb4aeb287e3d484063ed49881a143f150a4ba408430de9e3"
build_start_time: [1754041335, 916468000]
build_end_time: [1754041346, 142076000]
inputs:
  "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/ContentView.swift": [1753897193, 28528439]
  ? "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Managers/DownloadManager.swift"
  : [1753873786, 747776586]
  ? "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/YouTubeTranscriptDownloaderApp.swift"
  : [1753870596, 175367951]
  ? "/Users/<USER>/Downloads/Apps/Scripture 3/build/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/YouTubeTranscriptDownloader/IntermediateBuildFilesPath/YouTubeTranscriptDownloader.build/Release/YouTubeTranscriptDownloader.build/DerivedSources/GeneratedAssetSymbols.swift"
  : [1754041335, 834210369]
