{"appPreferencesBuildSettings": {}, "buildConfigurations": [{"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++20", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_TESTABILITY": "YES", "ENABLE_USER_SCRIPT_SANDBOXING": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu17", "GCC_DYNAMIC_NO_PIC": "NO", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PREPROCESSOR_DEFINITIONS": "DEBUG=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "LOCALIZATION_PREFERS_STRING_CATALOGS": "YES", "MACOSX_DEPLOYMENT_TARGET": "14.0", "MTL_ENABLE_DEBUG_INFO": "INCLUDE_SOURCE", "MTL_FAST_MATH": "YES", "ONLY_ACTIVE_ARCH": "YES", "SDKROOT": "macosx", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "DEBUG $(inherited)", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>"}, "guid": "77dc07c15c29080e076455d5e8c2ee60e7fe6b008d1d3b5d636786641c7ba097", "name": "Debug"}, {"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++20", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_USER_SCRIPT_SANDBOXING": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu17", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "LOCALIZATION_PREFERS_STRING_CATALOGS": "YES", "MACOSX_DEPLOYMENT_TARGET": "14.0", "MTL_ENABLE_DEBUG_INFO": "NO", "MTL_FAST_MATH": "YES", "SDKROOT": "macosx", "SWIFT_COMPILATION_MODE": "wholemodule"}, "guid": "77dc07c15c29080e076455d5e8c2ee6071d60be7d6a685fc9b401069500e658f", "name": "Release"}], "classPrefix": "", "defaultConfigurationName": "Release", "developmentRegion": "en", "groupTree": {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "77dc07c15c29080e076455d5e8c2ee60670bf8a0f0ab3139706813527e7ea361", "path": "YouTubeTranscriptDownloaderApp.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "77dc07c15c29080e076455d5e8c2ee6010e5296ce924bb3c44cbfbcc6fb6c78c", "path": "ContentView.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.swift", "guid": "77dc07c15c29080e076455d5e8c2ee60dd801da8b8c18ccc6639c325af8ad3bc", "path": "DownloadManager.swift", "sourceTree": "<group>", "type": "file"}], "guid": "77dc07c15c29080e076455d5e8c2ee60476eeec22550726c8841c7d75c4f12ea", "name": "Managers", "path": "Managers", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.python", "guid": "77dc07c15c29080e076455d5e8c2ee6055dceb4f5e2f35f8d85433fda82e7a96", "path": "download_transcript.py", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.python", "guid": "77dc07c15c29080e076455d5e8c2ee60ab449d33efc330e54e7733b8e85fd0e2", "path": "install_dependencies.py", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "77dc07c15c29080e076455d5e8c2ee60e8089051686303ad6660c7dfda4b68b5", "path": "requirements.txt", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.python", "guid": "77dc07c15c29080e076455d5e8c2ee60e96cacfd1fe366949e4302476e73ff30", "path": "get_channel_videos.py", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.python", "guid": "77dc07c15c29080e076455d5e8c2ee6022b9c03d0e079d997bd5b6c59867dd1a", "path": "get_channel_thumbnail.py", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.python", "guid": "77dc07c15c29080e076455d5e8c2ee6072f7e35b00885f06e87b5a0c860e932f", "path": "download_transcript_enhanced.py", "sourceTree": "<group>", "type": "file"}], "guid": "77dc07c15c29080e076455d5e8c2ee60fb949886bc5e033dde2dfad5012e9ef8", "name": "Python", "path": "Python", "sourceTree": "<group>", "type": "group"}, {"fileType": "folder.assetcatalog", "guid": "77dc07c15c29080e076455d5e8c2ee607662f1ec9bfd6b8fbe03fcdb500cc12a", "path": "Assets.xcassets", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "77dc07c15c29080e076455d5e8c2ee60ad403c7a107efe9df53959534f3d52f9", "path": "Info.plist", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "folder.assetcatalog", "guid": "77dc07c15c29080e076455d5e8c2ee602fce842c9510904d6f3615bdf2740ef2", "path": "Preview Assets.xcassets", "sourceTree": "<group>", "type": "file"}], "guid": "77dc07c15c29080e076455d5e8c2ee604306a789ee84694a63a6569242daaaa5", "name": "Preview Content", "path": "Preview Content", "sourceTree": "<group>", "type": "group"}], "guid": "77dc07c15c29080e076455d5e8c2ee6075a19b51a0da8211c1b708473a7f337f", "name": "YouTubeTranscriptDownloader", "path": "YouTubeTranscriptDownloader", "sourceTree": "<group>", "type": "group"}, {"guid": "77dc07c15c29080e076455d5e8c2ee60fe53178136c41d2df9c4f43d0ed2e483", "name": "Products", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "77dc07c15c29080e076455d5e8c2ee60101b402c5e2cf02c737fd3b428501178", "name": "YouTubeTranscriptDownloader", "path": "", "sourceTree": "<group>", "type": "group"}, "guid": "77dc07c15c29080e076455d5e8c2ee60", "path": "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader.xcodeproj", "projectDirectory": "/Users/<USER>/Downloads/Apps/Scripture/Scripture 3/YouTubeTranscriptDownloader", "targets": ["TARGET@v11_hash=9763c3f90b599eedafaebc7ea7a8720d"]}