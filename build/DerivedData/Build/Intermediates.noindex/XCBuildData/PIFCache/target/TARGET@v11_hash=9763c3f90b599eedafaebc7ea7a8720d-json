{"buildConfigurations": [{"buildSettings": {"ASSETCATALOG_COMPILER_APPICON_NAME": "AppIcon", "ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME": "AccentColor", "CODE_SIGN_ENTITLEMENTS": "YouTubeTranscriptDownloader/YouTubeTranscriptDownloader.entitlements", "CODE_SIGN_STYLE": "Automatic", "COMBINE_HIDPI_IMAGES": "YES", "CURRENT_PROJECT_VERSION": "1", "DEVELOPMENT_ASSET_PATHS": "\"YouTubeTranscriptDownloader/Preview Content\"", "ENABLE_PREVIEWS": "YES", "GENERATE_INFOPLIST_FILE": "YES", "INFOPLIST_KEY_NSHumanReadableCopyright": "", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/../Frameworks", "MARKETING_VERSION": "1.0", "PRODUCT_BUNDLE_IDENTIFIER": "com.yourcompany.YouTubeTranscriptDownloader", "PRODUCT_NAME": "Scripture", "SWIFT_EMIT_LOC_STRINGS": "YES", "SWIFT_VERSION": "5.0"}, "guid": "77dc07c15c29080e076455d5e8c2ee6007b7a7ad6c426fe2e5ddba1ec1d81c69", "name": "Debug"}, {"buildSettings": {"ASSETCATALOG_COMPILER_APPICON_NAME": "AppIcon", "ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME": "AccentColor", "CODE_SIGN_ENTITLEMENTS": "YouTubeTranscriptDownloader/YouTubeTranscriptDownloader.entitlements", "CODE_SIGN_STYLE": "Automatic", "COMBINE_HIDPI_IMAGES": "YES", "CURRENT_PROJECT_VERSION": "1", "DEVELOPMENT_ASSET_PATHS": "\"YouTubeTranscriptDownloader/Preview Content\"", "ENABLE_PREVIEWS": "YES", "GENERATE_INFOPLIST_FILE": "YES", "INFOPLIST_KEY_NSHumanReadableCopyright": "", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/../Frameworks", "MARKETING_VERSION": "1.0", "PRODUCT_BUNDLE_IDENTIFIER": "com.yourcompany.YouTubeTranscriptDownloader", "PRODUCT_NAME": "Scripture", "SWIFT_EMIT_LOC_STRINGS": "YES", "SWIFT_VERSION": "5.0"}, "guid": "77dc07c15c29080e076455d5e8c2ee60a796a275d5fe32f3cb71af0458f8dc4b", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "77dc07c15c29080e076455d5e8c2ee6010e5296ce924bb3c44cbfbcc6fb6c78c", "guid": "77dc07c15c29080e076455d5e8c2ee60c274b0894114e690e010680e61493f20"}, {"fileReference": "77dc07c15c29080e076455d5e8c2ee60dd801da8b8c18ccc6639c325af8ad3bc", "guid": "77dc07c15c29080e076455d5e8c2ee60274650b07db439a624de7a7f9d03f2ba"}, {"fileReference": "77dc07c15c29080e076455d5e8c2ee60670bf8a0f0ab3139706813527e7ea361", "guid": "77dc07c15c29080e076455d5e8c2ee6066e683cb70547b0e3859ab599a762f8b"}], "guid": "77dc07c15c29080e076455d5e8c2ee60ff40a0bf65f311bc868cd67ca63afc30", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "77dc07c15c29080e076455d5e8c2ee60df16de63d3929c71d69abf592bbc18ba", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "77dc07c15c29080e076455d5e8c2ee602fce842c9510904d6f3615bdf2740ef2", "guid": "77dc07c15c29080e076455d5e8c2ee60b34cc791bef3aefc348f692e07cbee92"}, {"fileReference": "77dc07c15c29080e076455d5e8c2ee607662f1ec9bfd6b8fbe03fcdb500cc12a", "guid": "77dc07c15c29080e076455d5e8c2ee6023e856de7bde7f256114b0fe23089869"}, {"fileReference": "77dc07c15c29080e076455d5e8c2ee6055dceb4f5e2f35f8d85433fda82e7a96", "guid": "77dc07c15c29080e076455d5e8c2ee60c211c685e0d4386cbb908fd61b5a7fb3"}, {"fileReference": "77dc07c15c29080e076455d5e8c2ee60ab449d33efc330e54e7733b8e85fd0e2", "guid": "77dc07c15c29080e076455d5e8c2ee6075f4c7dc28a9bf3e676b85500b961bc0"}, {"fileReference": "77dc07c15c29080e076455d5e8c2ee60e8089051686303ad6660c7dfda4b68b5", "guid": "77dc07c15c29080e076455d5e8c2ee6012ea00cb2493ef63fc4af54e5c51a86e"}, {"fileReference": "77dc07c15c29080e076455d5e8c2ee60e96cacfd1fe366949e4302476e73ff30", "guid": "77dc07c15c29080e076455d5e8c2ee60a1fbb66ec3639ccf4a911b36cc8145ac"}, {"fileReference": "77dc07c15c29080e076455d5e8c2ee6022b9c03d0e079d997bd5b6c59867dd1a", "guid": "77dc07c15c29080e076455d5e8c2ee60371ce35bf4208f2f6a2e03992952833f"}, {"fileReference": "77dc07c15c29080e076455d5e8c2ee6072f7e35b00885f06e87b5a0c860e932f", "guid": "77dc07c15c29080e076455d5e8c2ee6095f338b0c64f28676761f26b51344abc"}], "guid": "77dc07c15c29080e076455d5e8c2ee6068c1b1b9f2a83137c3e123cebf836126", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788", "name": "YouTubeTranscriptDownloader", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "77dc07c15c29080e076455d5e8c2ee6083a67c2ffc6f924b661d2a3d67de91f3", "name": "Scripture.app", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.application", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "$(PRODUCT_BUNDLE_IDENTIFIER)", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "$(PRODUCT_BUNDLE_IDENTIFIER)", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}