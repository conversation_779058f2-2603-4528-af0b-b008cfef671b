<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>logFormatVersion</key>
	<integer>11</integer>
	<key>logs</key>
	<dict>
		<key>01C0D77B-54AF-4043-A3CD-47DD04CA2D90</key>
		<dict>
			<key>className</key>
			<string>IDECommandLineBuildLog</string>
			<key>documentTypeString</key>
			<string>&lt;nil&gt;</string>
			<key>domainType</key>
			<string>Xcode.IDEActivityLogDomainType.BuildLog</string>
			<key>fileName</key>
			<string>01C0D77B-54AF-4043-A3CD-47DD04CA2D90.xcactivitylog</string>
			<key>hasPrimaryLog</key>
			<true/>
			<key>primaryObservable</key>
			<dict>
				<key>highLevelStatus</key>
				<string>W</string>
				<key>totalNumberOfAnalyzerIssues</key>
				<integer>0</integer>
				<key>totalNumberOfErrors</key>
				<integer>0</integer>
				<key>totalNumberOfTestFailures</key>
				<integer>0</integer>
				<key>totalNumberOfWarnings</key>
				<integer>1</integer>
			</dict>
			<key>schemeIdentifier-containerName</key>
			<string>YouTubeTranscriptDownloader project</string>
			<key>schemeIdentifier-schemeName</key>
			<string>YouTubeTranscriptDownloader</string>
			<key>schemeIdentifier-sharedScheme</key>
			<integer>1</integer>
			<key>signature</key>
			<string>Archiving project YouTubeTranscriptDownloader with scheme YouTubeTranscriptDownloader</string>
			<key>timeStartedRecording</key>
			<real>775734134.30962896</real>
			<key>timeStoppedRecording</key>
			<real>775734146.14535201</real>
			<key>title</key>
			<string>Archiving project YouTubeTranscriptDownloader with scheme YouTubeTranscriptDownloader</string>
			<key>uniqueIdentifier</key>
			<string>01C0D77B-54AF-4043-A3CD-47DD04CA2D90</string>
		</dict>
	</dict>
</dict>
</plist>
