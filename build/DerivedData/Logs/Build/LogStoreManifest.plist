<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>logFormatVersion</key>
	<integer>11</integer>
	<key>logs</key>
	<dict>
		<key>66B06A75-B050-4AD0-B806-6942AA0D477B</key>
		<dict>
			<key>className</key>
			<string>IDECommandLineBuildLog</string>
			<key>documentTypeString</key>
			<string>&lt;nil&gt;</string>
			<key>domainType</key>
			<string>Xcode.IDEActivityLogDomainType.BuildLog</string>
			<key>fileName</key>
			<string>66B06A75-B050-4AD0-B806-6942AA0D477B.xcactivitylog</string>
			<key>hasPrimaryLog</key>
			<true/>
			<key>primaryObservable</key>
			<dict>
				<key>highLevelStatus</key>
				<string>W</string>
				<key>totalNumberOfAnalyzerIssues</key>
				<integer>0</integer>
				<key>totalNumberOfErrors</key>
				<integer>0</integer>
				<key>totalNumberOfTestFailures</key>
				<integer>0</integer>
				<key>totalNumberOfWarnings</key>
				<integer>1</integer>
			</dict>
			<key>schemeIdentifier-containerName</key>
			<string>YouTubeTranscriptDownloader project</string>
			<key>schemeIdentifier-schemeName</key>
			<string>YouTubeTranscriptDownloader</string>
			<key>schemeIdentifier-sharedScheme</key>
			<integer>1</integer>
			<key>signature</key>
			<string>Archiving project YouTubeTranscriptDownloader with scheme YouTubeTranscriptDownloader</string>
			<key>timeStartedRecording</key>
			<real>775739768.441818</real>
			<key>timeStoppedRecording</key>
			<real>775739780.43934405</real>
			<key>title</key>
			<string>Archiving project YouTubeTranscriptDownloader with scheme YouTubeTranscriptDownloader</string>
			<key>uniqueIdentifier</key>
			<string>66B06A75-B050-4AD0-B806-6942AA0D477B</string>
		</dict>
	</dict>
</dict>
</plist>
