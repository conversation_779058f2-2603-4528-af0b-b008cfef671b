#!/usr/bin/env python3
"""
<PERSON>ript to install Python dependencies for YouTube Transcript Downloader
"""

import subprocess
import sys
import json

def install_dependencies():
    """Install required Python packages"""
    try:
        # Install pytubefix
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "pytubefix>=9.4.1"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print(json.dumps({
                "message": "Dependencies installed successfully",
                "success": True
            }))
            return 0
        else:
            print(json.dumps({
                "error": f"Failed to install dependencies: {result.stderr}",
                "success": False
            }))
            return 1
            
    except Exception as e:
        print(json.dumps({
            "error": f"Error installing dependencies: {str(e)}",
            "success": False
        }))
        return 1

if __name__ == "__main__":
    sys.exit(install_dependencies())
