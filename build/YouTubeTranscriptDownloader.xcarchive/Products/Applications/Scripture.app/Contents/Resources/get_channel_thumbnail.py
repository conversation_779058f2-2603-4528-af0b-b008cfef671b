#!/usr/bin/env python3
"""
<PERSON>ript to get channel thumbnail URL using pytubefix
Usage: python3 get_channel_thumbnail.py <channel_url>
"""

import sys
import json
import os

# Create a test file to verify the script is being called
test_file = "/tmp/channel_script_called.txt"
with open(test_file, "w") as f:
    f.write(f"<PERSON><PERSON><PERSON> called with args: {sys.argv}\n")

def install_pytubefix():
    """Automatically install pytubefix if missing"""
    try:
        import subprocess
        print("pytubefix not found. Installing automatically...", file=sys.stderr)
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "pytubefix>=9.4.1"
        ], capture_output=True, text=True)

        if result.returncode == 0:
            print("pytubefix installed successfully!", file=sys.stderr)
            return True
        else:
            print(f"Failed to install pytubefix: {result.stderr}", file=sys.stderr)
            return False
    except Exception as e:
        print(f"Error installing pytubefix: {str(e)}", file=sys.stderr)
        return False

# Try to import pytubefix, install if missing
try:
    from pytubefix import Channel
    PYTUBEFIX_AVAILABLE = True
except ImportError:
    print("pytubefix not found, attempting automatic installation...", file=sys.stderr)
    if install_pytubefix():
        try:
            from pytubefix import Channel
            PYTUBEFIX_AVAILABLE = True
            print("pytubefix imported successfully after installation!", file=sys.stderr)
        except ImportError:
            PYTUBEFIX_AVAILABLE = False
            print("Failed to import pytubefix even after installation", file=sys.stderr)
    else:
        PYTUBEFIX_AVAILABLE = False

def get_channel_thumbnail(channel_url):
    """Get channel thumbnail URL using pytubefix"""
    try:
        if not PYTUBEFIX_AVAILABLE:
            print(json.dumps({"error": "Failed to install or import pytubefix library. Please install manually: pip3 install pytubefix", "success": False}))
            return 1

        print(f"Fetching channel info for: {channel_url}", file=sys.stderr)

        # Create channel object
        channel = Channel(channel_url)

        # Get channel info
        channel_name = channel.channel_name
        print(f"Channel name: {channel_name}", file=sys.stderr)

        # Get thumbnail URL - pytubefix Channel class has thumbnail_url property
        thumbnail_url = None
        if hasattr(channel, 'thumbnail_url') and channel.thumbnail_url:
            thumbnail_url = channel.thumbnail_url
            print(f"Found thumbnail_url: {thumbnail_url}", file=sys.stderr)
        else:
            print("No thumbnail_url found", file=sys.stderr)

        # Get channel ID
        channel_id = getattr(channel, 'channel_id', None)
        print(f"Channel ID: {channel_id}", file=sys.stderr)

        print(json.dumps({
            "success": True,
            "channel_name": channel_name,
            "thumbnail_url": thumbnail_url,
            "channel_id": channel_id
        }))
        return 0

    except Exception as e:
        print(f"Error: {str(e)}", file=sys.stderr)
        print(json.dumps({
            "error": str(e),
            "success": False
        }))
        return 1

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print(json.dumps({"error": "Channel URL required", "success": False}))
        sys.exit(1)

    channel_url = sys.argv[1]
    sys.exit(get_channel_thumbnail(channel_url))
