<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Resources/AppIcon.icns</key>
		<data>
		H2U+jbqSfGcUKu7lM6HPcTOMh3U=
		</data>
		<key>Resources/Assets.car</key>
		<data>
		xB60OyvYTtVAHUZ17QZRjQuqIC4=
		</data>
		<key>Resources/download_transcript.py</key>
		<data>
		C00N2EA77UttOKGL3d6tgPs6ees=
		</data>
		<key>Resources/download_transcript_enhanced.py</key>
		<data>
		ZyU/zkJkhmzVpSssnHyCnuN/+oc=
		</data>
		<key>Resources/get_channel_thumbnail.py</key>
		<data>
		51TNKjzoKVg2oOf68LYG1MUXrIA=
		</data>
		<key>Resources/get_channel_videos.py</key>
		<data>
		OFOqabOJGd3ik+bEJiBG5Ww5IYg=
		</data>
		<key>Resources/install_dependencies.py</key>
		<data>
		sSvu5qsPj39dC91KU6/QtJ67aho=
		</data>
		<key>Resources/requirements.txt</key>
		<data>
		JTI5LcD7HejH/fGl4hKt1qeF09w=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Resources/AppIcon.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			uPLxMJocqH3LRIomlnakECpoZCx2Ays9XoXu52eIlBg=
			</data>
		</dict>
		<key>Resources/Assets.car</key>
		<dict>
			<key>hash2</key>
			<data>
			qg9p30263zkKCgWNfoIyJAY2HHEXhJNYIaq9K2KhdzI=
			</data>
		</dict>
		<key>Resources/download_transcript.py</key>
		<dict>
			<key>hash2</key>
			<data>
			nHkIgRWeroUOfP6NChDOx9LP9oW2WCBH1cnRRfIr+28=
			</data>
		</dict>
		<key>Resources/download_transcript_enhanced.py</key>
		<dict>
			<key>hash2</key>
			<data>
			xDW2YmEMEtIXbzLrJwMFWDO06vmGXppXEd3hnyTdRlY=
			</data>
		</dict>
		<key>Resources/get_channel_thumbnail.py</key>
		<dict>
			<key>hash2</key>
			<data>
			ida16liFPVZJgbx6kES22h2AEer2rwNJp81QMCvuWwM=
			</data>
		</dict>
		<key>Resources/get_channel_videos.py</key>
		<dict>
			<key>hash2</key>
			<data>
			+CF/UNPTsR3wM6lSFYQ1PiXE99o+Ov/nzmSuD5TVhmo=
			</data>
		</dict>
		<key>Resources/install_dependencies.py</key>
		<dict>
			<key>hash2</key>
			<data>
			8dhk04xBHumBc7ywoBIxXzdaMaLzxUA5ZQ4zRT1uJhU=
			</data>
		</dict>
		<key>Resources/requirements.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			a/m97xguveI9/I5IVvvj60JV503towtGqUF1vxS6wzA=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^Resources/</key>
		<true/>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^(Frameworks|SharedFrameworks|PlugIns|Plug-ins|XPCServices|Helpers|MacOS|Library/(Automator|Spotlight|LoginItems))/</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^[^/]+$</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
