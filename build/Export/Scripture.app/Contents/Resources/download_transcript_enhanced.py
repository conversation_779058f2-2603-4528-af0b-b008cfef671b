#!/usr/bin/env python3
"""
Enhanced YouTube video transcript downloader with metadata
Usage: python3 download_transcript_enhanced.py <video_url> <output_dir> <video_title> [include_timestamps]
"""

import sys
import json
import os
import re
import time
from datetime import datetime

def install_pytubefix():
    """Automatically install pytubefix if missing"""
    try:
        import subprocess
        print("pytubefix not found. Installing automatically...", file=sys.stderr)
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "pytubefix>=9.4.1"
        ], capture_output=True, text=True)

        if result.returncode == 0:
            print("pytubefix installed successfully!", file=sys.stderr)
            return True
        else:
            print(f"Failed to install pytubefix: {result.stderr}", file=sys.stderr)
            return False
    except Exception as e:
        print(f"Error installing pytubefix: {str(e)}", file=sys.stderr)
        return False

# Try to import pytubefix, install if missing
try:
    from pytubefix import YouTube
    PYTUBEFIX_AVAILABLE = True
except ImportError:
    print("pytubefix not found, attempting automatic installation...", file=sys.stderr)
    if install_pytubefix():
        try:
            from pytubefix import YouTube
            PYTUBEFIX_AVAILABLE = True
            print("pytubefix imported successfully after installation!", file=sys.stderr)
        except ImportError:
            PYTUBEFIX_AVAILABLE = False
            print("Failed to import pytubefix even after installation", file=sys.stderr)
    else:
        PYTUBEFIX_AVAILABLE = False

def sanitize_filename(filename):
    """Remove invalid characters from filename"""
    filename = re.sub(r'[<>:"/\\|?*]', '', filename)
    filename = re.sub(r'\s+', ' ', filename)
    filename = filename.strip()
    if len(filename) > 200:
        filename = filename[:200]
    return filename

def format_number(num):
    """Format large numbers with commas"""
    if num is None:
        return "N/A"
    try:
        return f"{int(num):,}"
    except (ValueError, TypeError):
        return str(num)

def format_views_for_filename(views):
    """Format view count for filename (e.g., 1.2M views, 500K views, 1,234 views)"""
    if views is None:
        return "N/A views"
    try:
        views = int(views)
        if views >= 1_000_000:
            return f"{views / 1_000_000:.1f}M views"
        elif views >= 1_000:
            return f"{views / 1_000:.1f}K views"
        else:
            return f"{views:,} views"
    except (ValueError, TypeError):
        return "N/A views"

def download_transcript_enhanced(video_url, output_dir, video_title, include_timestamps=True, preferred_language_codes=None, use_folders=True, channel_name=""):
    """Download transcript with enhanced metadata and language preference support"""
    try:
        if not PYTUBEFIX_AVAILABLE:
            print(json.dumps({
                "error": "Failed to install or import pytubefix library. Please install manually: pip3 install pytubefix",
                "success": False
            }))
            return 1

        print(f"Processing video: {video_url}", file=sys.stderr)
        
        # Create YouTube object
        yt = YouTube(video_url)
        
        # Get video metadata
        actual_title = yt.title
        video_id = yt.video_id
        author = yt.author
        views = yt.views
        likes = getattr(yt, 'likes', None)
        publish_date = getattr(yt, 'publish_date', None)
        length = yt.length
        description = getattr(yt, 'description', '')

        # Always try to get transcript - let the caption availability determine success
        # Don't skip based on live status or duration, as archived streams often have transcripts
        if length is None or length == 0:
            print(f"Video has no duration info (possibly live/archived stream): {actual_title}", file=sys.stderr)
        else:
            print(f"Video duration: {length} seconds", file=sys.stderr)

        print(f"Title: {actual_title}", file=sys.stderr)
        print(f"Author: {author}", file=sys.stderr)
        print(f"Views: {format_number(views)}", file=sys.stderr)
        print(f"Likes: {format_number(likes)}", file=sys.stderr)
        print(f"Published: {publish_date}", file=sys.stderr)
        print(f"Duration: {length} seconds", file=sys.stderr)
        
        # Create filename with new format: [ChannelName] [LIVE] VideoTitle - 1.2M views.txt
        base_title = video_title if video_title else actual_title

        # Determine if this is a live video (check if channel_name contains "/live" or if it's a live stream)
        is_live = "/live" in channel_name if channel_name else False

        # Clean channel name (remove "/live" suffix if present)
        clean_channel_name = channel_name.replace("/live", "") if channel_name else "Unknown Channel"

        # Format view count for filename
        views_text = format_views_for_filename(views)

        # Build filename components
        filename_parts = []
        if clean_channel_name and clean_channel_name != "Unknown Channel" and not use_folders:
            filename_parts.append(f"[{clean_channel_name}]")
        if is_live:
            filename_parts.append("[LIVE]")
        filename_parts.append(base_title)
        filename_parts.append(f"- {views_text}")

        # Join parts and sanitize
        filename = " ".join(filename_parts)
        safe_filename = sanitize_filename(filename)
        transcript_file = os.path.join(output_dir, f"{safe_filename}.txt")

        # Check for existing files with different view counts and remove them
        if not use_folders:  # Only do this for single folder mode
            try:
                # Look for existing files with same channel and title but different view counts
                base_pattern = f"[{clean_channel_name}]"
                if is_live:
                    base_pattern += " [LIVE]"
                base_pattern += f" {sanitize_filename(base_title)} -"

                for existing_file in os.listdir(output_dir):
                    if existing_file.startswith(base_pattern) and existing_file.endswith(".txt") and existing_file != f"{safe_filename}.txt":
                        old_file_path = os.path.join(output_dir, existing_file)
                        print(f"Removing old file with outdated view count: {existing_file}", file=sys.stderr)
                        os.remove(old_file_path)
            except Exception as e:
                print(f"Error checking for existing files: {e}", file=sys.stderr)
        
        # Get captions/transcript
        captions = yt.captions
        transcript_text = ""
        transcript_source = "none"

        print(f"Available captions: {list(captions.keys()) if captions else 'None'}", file=sys.stderr)

        if captions:
            # Try to get captions based on preferred language, then fallback
            caption = None

            # Use preferred language codes if provided, otherwise default to English
            if preferred_language_codes:
                language_codes_to_try = preferred_language_codes
            else:
                language_codes_to_try = ['en', 'a.en', 'en-US', 'en-GB']

            # First try preferred language codes
            for lang_code in language_codes_to_try:
                if lang_code in captions:
                    caption = captions[lang_code]
                    transcript_source = f"captions ({lang_code})"
                    print(f"Using preferred language {lang_code} captions", file=sys.stderr)
                    break

            # If no preferred language found, try English as fallback (unless it was already tried)
            if not caption and preferred_language_codes and not any(code.startswith('en') for code in preferred_language_codes):
                for lang_code in ['en', 'a.en', 'en-US', 'en-GB']:
                    if lang_code in captions:
                        caption = captions[lang_code]
                        transcript_source = f"captions ({lang_code})"
                        print(f"Fallback to English caption: {lang_code}", file=sys.stderr)
                        break

            if not caption:
                # Get first available caption
                available_langs = list(captions.keys())
                if available_langs:
                    caption = captions[available_langs[0]]
                    transcript_source = f"captions ({available_langs[0]})"
                    print(f"Using {available_langs[0]} captions (first available)", file=sys.stderr)

            if caption:
                try:
                    if include_timestamps:
                        transcript_text = caption.generate_srt_captions()
                    else:
                        transcript_text = caption.generate_txt_captions()
                    print(f"Successfully generated transcript from {transcript_source}", file=sys.stderr)
                except Exception as e:
                    print(f"Error generating captions: {e}", file=sys.stderr)
                    transcript_text = f"Could not generate transcript from {transcript_source}: {str(e)}"
                    transcript_source = "error"

        if not transcript_text:
            # Check if this might be an archived stream
            if length is None or length == 0:
                transcript_text = "No transcript available for this video (archived live stream - transcript may not have been generated)"
                transcript_source = "archived_stream"
            else:
                transcript_text = "No transcript available for this video"
                transcript_source = "unavailable"
            print(f"No transcript found. Source: {transcript_source}", file=sys.stderr)
        
        # Create metadata header
        metadata_lines = [
            f"Title: {actual_title}",
            f"Author: {author}",
            f"Video URL: {video_url}",
            f"Video ID: {video_id}",
            f"Views: {format_number(views)}",
            f"Likes: {format_number(likes)}",
            f"Published: {publish_date.strftime('%Y-%m-%d %H:%M:%S') if publish_date else 'N/A'}",
            f"Duration: {length//60}:{length%60:02d}" if length else "Duration: N/A",
            f"Downloaded: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            "",
            "=" * 80,
            ""
        ]
        
        # Combine metadata and transcript
        content = "\n".join(metadata_lines) + transcript_text
        
        # Create output directory
        os.makedirs(output_dir, exist_ok=True)
        
        # Write to file
        with open(transcript_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        # Set file modification time to video publish date
        if publish_date:
            try:
                publish_timestamp = publish_date.timestamp()
                os.utime(transcript_file, (publish_timestamp, publish_timestamp))
                print(f"Set file date to: {publish_date}", file=sys.stderr)
            except Exception as e:
                print(f"Could not set file date: {e}", file=sys.stderr)
        
        print(json.dumps({
            "success": True,
            "title": actual_title,
            "filename": os.path.basename(transcript_file),
            "filepath": transcript_file,
            "metadata": {
                "author": author,
                "views": views,
                "likes": likes,
                "publish_date": publish_date.isoformat() if publish_date else None,
                "length": length,
                "video_id": video_id
            }
        }, default=str))
        return 0
        
    except Exception as e:
        print(f"Error: {str(e)}", file=sys.stderr)
        print(json.dumps({
            "error": str(e),
            "success": False
        }))
        return 1

if __name__ == "__main__":
    if len(sys.argv) < 4:
        print(json.dumps({
            "error": "Usage: python3 download_transcript_enhanced.py <video_url> <output_dir> <video_title> [include_timestamps] [preferred_languages] [use_folders] [channel_name]",
            "success": False
        }))
        sys.exit(1)

    video_url = sys.argv[1]
    output_dir = sys.argv[2]
    video_title = sys.argv[3]
    include_timestamps = len(sys.argv) > 4 and sys.argv[4].lower() == 'true'

    # Parse preferred language codes (comma-separated list)
    preferred_language_codes = None
    if len(sys.argv) > 5 and sys.argv[5]:
        preferred_language_codes = [code.strip() for code in sys.argv[5].split(',') if code.strip()]

    # Parse use_folders flag
    use_folders = len(sys.argv) > 6 and sys.argv[6].lower() == 'true'

    # Parse channel name
    channel_name = sys.argv[7] if len(sys.argv) > 7 else ""

    sys.exit(download_transcript_enhanced(video_url, output_dir, video_title, include_timestamps, preferred_language_codes, use_folders, channel_name))
