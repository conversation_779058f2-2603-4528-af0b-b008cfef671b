#!/usr/bin/env python3
"""
Get video URLs from a YouTube channel
"""

import sys
import json
import re
import os

def install_pytubefix():
    """Automatically install pytubefix if missing"""
    try:
        import subprocess
        print("pytubefix not found. Installing automatically...", file=sys.stderr)
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "pytubefix>=9.4.1"
        ], capture_output=True, text=True)

        if result.returncode == 0:
            print("pytubefix installed successfully!", file=sys.stderr)
            return True
        else:
            print(f"Failed to install pytubefix: {result.stderr}", file=sys.stderr)
            return False
    except Exception as e:
        print(f"Error installing pytubefix: {str(e)}", file=sys.stderr)
        return False

# Try to import pytubefix, install if missing
try:
    from pytubefix import Channel
    PYTUBEFIX_AVAILABLE = True
except ImportError:
    print("pytubefix not found, attempting automatic installation...", file=sys.stderr)
    if install_pytubefix():
        try:
            from pytubefix import Channel
            PYTUBEFIX_AVAILABLE = True
            print("pytubefix imported successfully after installation!", file=sys.stderr)
        except ImportError:
            PYTUBEFIX_AVAILABLE = False
            print("Failed to import pytubefix even after installation", file=sys.stderr)
    else:
        PYTUBEFIX_AVAILABLE = False

def get_channel_videos(channel_url, max_live_videos=25, max_regular_videos=25):
    """Get video URLs from a YouTube channel - both live and regular videos"""
    try:
        if not PYTUBEFIX_AVAILABLE:
            return {
                "error": "Failed to install or import pytubefix library. Please install manually: pip3 install pytubefix",
                "success": False,
                "videos": []
            }
        
        # Validate channel URL format
        if not channel_url or not any(pattern in channel_url for pattern in [
            'youtube.com/channel/',
            'youtube.com/c/',
            'youtube.com/@',
            'youtube.com/user/'
        ]):
            return {
                "error": f"Invalid YouTube channel URL format: {channel_url}",
                "success": False,
                "videos": []
            }

        # Also try to get live streams by modifying the URL
        streams_url = None
        if '/@' in channel_url:
            streams_url = channel_url.rstrip('/') + '/streams'
        elif '/channel/' in channel_url:
            streams_url = channel_url.rstrip('/') + '/streams'
        elif '/c/' in channel_url:
            streams_url = channel_url.rstrip('/') + '/streams'
        
        print(f"Getting videos from channel: {channel_url}", file=sys.stderr)
        
        # Create channel object
        channel = Channel(channel_url)
        
        # Get channel info
        channel_name = channel.channel_name
        print(f"Channel name: {channel_name}", file=sys.stderr)
        
        # Get videos (limited to max_videos)
        videos = []
        video_count = 0

        # Get BOTH live and regular videos separately
        live_video_objects = []
        regular_video_objects = []

        try:
            print("Fetching live and regular videos from channel...", file=sys.stderr)

            # STEP 1: Get Live Videos (Priority)
            print(f"=== FETCHING LIVE VIDEOS (limit: {max_live_videos}) ===", file=sys.stderr)
            try:
                # Create a fresh channel object specifically for live streams to avoid caching issues
                fresh_channel = Channel(channel_url)
                if hasattr(fresh_channel, 'live'):
                    print(f"Getting live streams from fresh channel.live property", file=sys.stderr)

                    live_count = 0
                    seen_live_ids = set()
                    for video_obj in fresh_channel.live:
                        if live_count >= max_live_videos:
                            break

                        video_id = getattr(video_obj, 'video_id', None)
                        if video_id and video_id not in seen_live_ids:
                            live_video_objects.append(video_obj)
                            seen_live_ids.add(video_id)
                            live_count += 1

                            # Get title for logging
                            try:
                                title = getattr(video_obj, 'title', 'Unknown')
                            except:
                                title = 'Unknown'

                            print(f"Added LIVE video {live_count}: {title} (ID: {video_id})", file=sys.stderr)

                    print(f"Collected {live_count} live videos", file=sys.stderr)
                else:
                    print("No channel.live property found", file=sys.stderr)
            except Exception as e:
                print(f"Could not get live videos: {e}", file=sys.stderr)

            # STEP 2: Get Regular Videos
            print(f"=== FETCHING REGULAR VIDEOS (limit: {max_regular_videos}) ===", file=sys.stderr)
            try:
                regular_count = 0
                seen_regular_ids = set()
                seen_live_ids = {getattr(obj, 'video_id', None) for obj in live_video_objects}

                for video_obj in channel.video_urls:
                    if regular_count >= max_regular_videos:
                        break

                    video_id = getattr(video_obj, 'video_id', None)
                    # Skip if it's already in live videos or already seen
                    if video_id and video_id not in seen_live_ids and video_id not in seen_regular_ids:
                        regular_video_objects.append(video_obj)
                        seen_regular_ids.add(video_id)
                        regular_count += 1

                        # Get title for logging
                        try:
                            title = getattr(video_obj, 'title', 'Unknown')
                        except:
                            title = 'Unknown'

                        print(f"Added REGULAR video {regular_count}: {title} (ID: {video_id})", file=sys.stderr)

                print(f"Collected {regular_count} regular videos", file=sys.stderr)
            except Exception as e:
                print(f"Could not get regular videos: {e}", file=sys.stderr)

            # Combine all video objects
            video_objects = live_video_objects + regular_video_objects
            print(f"Total videos collected: {len(video_objects)} (Live: {len(live_video_objects)}, Regular: {len(regular_video_objects)})", file=sys.stderr)

        except Exception as e:
            print(f"Error getting video URLs: {str(e)}", file=sys.stderr)
            return {
                "error": f"Error getting video URLs from channel: {str(e)}",
                "success": False,
                "videos": []
            }

        print(f"Found {len(video_objects)} video objects", file=sys.stderr)

        # Process each video object
        for video_obj in video_objects:
            try:
                # Extract video ID and construct URL
                video_id = getattr(video_obj, 'video_id', None)
                if not video_id:
                    print(f"No video_id found for video object: {video_obj}", file=sys.stderr)
                    continue

                video_url = f"https://www.youtube.com/watch?v={video_id}"

                # Get video metadata with better error handling
                try:
                    video_length = getattr(video_obj, 'length', None)
                except Exception as e:
                    print(f"Error getting video length: {e}", file=sys.stderr)
                    video_length = None

                try:
                    is_live = getattr(video_obj, 'is_live', False)
                except Exception as e:
                    print(f"Error getting live status: {e}", file=sys.stderr)
                    is_live = False

                try:
                    video_title = getattr(video_obj, 'title', f"Video {video_count + 1}")
                    if not video_title or video_title.strip() == "":
                        video_title = f"Video {video_count + 1}"
                except Exception as e:
                    print(f"Error getting video title: {e}", file=sys.stderr)
                    video_title = f"Video {video_count + 1}"

                # Include all videos - let transcript download determine if content is available
                if video_length is None or video_length == 0:
                    print(f"Including video with no duration (live/archived stream): {video_title}", file=sys.stderr)
                else:
                    print(f"Including video: {video_title} (Duration: {video_length}s)", file=sys.stderr)

                # Handle publish_date serialization with better error handling
                publish_date = None
                try:
                    publish_date = getattr(video_obj, 'publish_date', None)
                    if publish_date:
                        try:
                            publish_date = publish_date.isoformat()
                        except:
                            publish_date = str(publish_date)
                except Exception as e:
                    print(f"Error getting publish date: {e}", file=sys.stderr)
                    publish_date = None

                # Handle views with error handling
                views = None
                try:
                    views = getattr(video_obj, 'views', None)
                except Exception as e:
                    print(f"Error getting views: {e}", file=sys.stderr)
                    views = None

                video_info = {
                    "url": video_url,
                    "title": video_title,
                    "video_id": video_id,
                    "length": video_length,
                    "views": views,
                    "publish_date": publish_date
                }

                videos.append(video_info)
                video_count += 1

                duration_str = f"{video_length}s" if video_length else "Unknown"
                print(f"Added video {video_count}: {video_info['title']} (Duration: {duration_str})", file=sys.stderr)

            except Exception as e:
                print(f"Error processing video object: {str(e)}", file=sys.stderr)
                print(f"Video object type: {type(video_obj)}", file=sys.stderr)
                continue
        
        # Separate live and regular videos in the response
        live_videos = []
        regular_videos = []

        for i, video in enumerate(videos):
            if i < len(live_video_objects):
                video['video_type'] = 'live'
                live_videos.append(video)
            else:
                video['video_type'] = 'regular'
                regular_videos.append(video)

        return {
            "success": True,
            "channel_name": channel_name,
            "channel_url": channel_url,
            "video_count": len(videos),
            "live_video_count": len(live_videos),
            "regular_video_count": len(regular_videos),
            "videos": videos,
            "live_videos": live_videos,
            "regular_videos": regular_videos
        }
        
    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        print(f"Full error traceback: {error_details}", file=sys.stderr)

        # Provide more specific error messages
        error_message = str(e)
        if "HTTP Error 404" in error_message:
            error_message = "Channel not found. The channel may be private, deleted, or the URL is incorrect."
        elif "HTTP Error 403" in error_message:
            error_message = "Access denied. The channel may be private or restricted."
        elif "network" in error_message.lower() or "connection" in error_message.lower():
            error_message = "Network connection failed. Check your internet connection and try again."
        elif "timeout" in error_message.lower():
            error_message = "Request timed out. The channel may be temporarily unavailable."
        else:
            error_message = f"Error accessing channel: {error_message}"

        return {
            "error": error_message,
            "success": False,
            "videos": []
        }

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print(json.dumps({"error": "Channel URL required", "success": False, "videos": []}))
        sys.exit(1)

    channel_url = sys.argv[1]

    # Support both old and new argument formats
    if len(sys.argv) == 3:
        # Old format: get_channel_videos.py URL max_videos
        max_videos = int(sys.argv[2])
        max_live_videos = max_videos // 2
        max_regular_videos = max_videos - max_live_videos
    elif len(sys.argv) == 4:
        # New format: get_channel_videos.py URL max_live_videos max_regular_videos
        max_live_videos = int(sys.argv[2])
        max_regular_videos = int(sys.argv[3])
    else:
        # Default values
        max_live_videos = 25
        max_regular_videos = 25

    result = get_channel_videos(channel_url, max_live_videos, max_regular_videos)
    print(json.dumps(result))

    sys.exit(0 if result["success"] else 1)
