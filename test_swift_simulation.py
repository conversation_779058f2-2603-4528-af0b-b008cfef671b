#!/usr/bin/env python3
"""
Test script to simulate exactly what the Swift app does
"""

import subprocess
import sys
import os

def test_python_execution():
    """Test running the Python script the same way Swift does"""
    
    # This simulates the Swift Process execution
    script_path = "YouTubeTranscriptDownloader/Python/download_transcript.py"
    video_url = "https://www.youtube.com/watch?v=dQw4w9WgXcQ"
    output_dir = "/tmp/swift_test"
    video_title = "Test Video from Swift"
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Run the same command that Swift runs
    cmd = ["/usr/bin/python3", script_path, video_url, output_dir, video_title]
    
    print(f"Running command: {' '.join(cmd)}")
    print(f"Working directory: {os.getcwd()}")
    print(f"Python path: {sys.executable}")
    
    try:
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=30
        )
        
        print(f"Return code: {result.returncode}")
        print(f"STDOUT: {result.stdout}")
        print(f"STDERR: {result.stderr}")
        
        if result.returncode == 0:
            print("SUCCESS: Command executed successfully")
        else:
            print("FAILED: Command failed")
            
    except subprocess.TimeoutExpired:
        print("TIMEOUT: Command timed out")
    except Exception as e:
        print(f"EXCEPTION: {e}")

if __name__ == "__main__":
    test_python_execution()
