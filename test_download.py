#!/usr/bin/env python3
"""
Test script to debug download issues
"""

import sys
import subprocess
import os

def test_python_environment():
    """Test if Python environment is working"""
    print("Python version:", sys.version)
    print("Python executable:", sys.executable)
    
    # Test pytubefix import
    try:
        import pytubefix
        print("pytubefix version:", pytubefix.__version__)
        print("pytubefix import: SUCCESS")
    except ImportError as e:
        print("pytubefix import: FAILED -", str(e))
        return False
    
    return True

def test_download_script():
    """Test the download script directly"""
    script_path = "/Users/<USER>/Library/Developer/Xcode/DerivedData/YouTubeTranscriptDownloader-gqqolxalimjsvqablkimwqcksrsh/Build/Products/Debug/YouTubeTranscriptDownloader.app/Contents/Resources/download_transcript.py"
    
    if not os.path.exists(script_path):
        print(f"Script not found at: {script_path}")
        return False
    
    print(f"Script found at: {script_path}")
    
    # Test with a simple YouTube URL
    test_url = "https://www.youtube.com/watch?v=dQw4w9WgXcQ"
    output_dir = "/tmp/test_download_debug"
    
    try:
        result = subprocess.run([
            sys.executable, script_path, test_url, output_dir, "Test Video"
        ], capture_output=True, text=True, timeout=30)
        
        print("Exit code:", result.returncode)
        print("STDOUT:", result.stdout)
        print("STDERR:", result.stderr)
        
        return result.returncode == 0
    except subprocess.TimeoutExpired:
        print("Script timed out")
        return False
    except Exception as e:
        print("Error running script:", str(e))
        return False

if __name__ == "__main__":
    print("=== Testing Python Environment ===")
    env_ok = test_python_environment()
    
    print("\n=== Testing Download Script ===")
    if env_ok:
        script_ok = test_download_script()
        print(f"Download script test: {'PASSED' if script_ok else 'FAILED'}")
    else:
        print("Skipping download script test due to environment issues")
