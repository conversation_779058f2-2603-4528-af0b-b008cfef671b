#!/bin/bash

# YouTube Transcript Downloader - One-Click Build Script
# Double-click this file to build the macOS application

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_header() {
    echo -e "${PURPLE}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${PURPLE}║${NC}                    ${CYAN}🚀 Scripture Builder 🚀${NC}                    ${PURPLE}║${NC}"
    echo -e "${PURPLE}╚══════════════════════════════════════════════════════════════╝${NC}"
    echo ""
}

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${CYAN}▶${NC} $1"
}

# Set error handling
set -e

# Clear screen and show header
clear
print_header

# Configuration
PROJECT_NAME="YouTubeTranscriptDownloader"
SCHEME_NAME="YouTubeTranscriptDownloader"
CONFIGURATION="Release"
BUILD_DIR="build"
DERIVED_DATA_PATH="$BUILD_DIR/DerivedData"

print_step "Starting build process..."
echo ""

# Check if we're in the right directory
if [ ! -d "$PROJECT_NAME" ]; then
    print_error "Project directory '$PROJECT_NAME' not found!"
    print_error "Please ensure this script is in the same directory as the $PROJECT_NAME folder."
    echo ""
    print_status "Press any key to exit..."
    read -n 1
    exit 1
fi

# Check if Xcode is installed
print_step "Checking Xcode installation..."
if ! command -v xcodebuild &> /dev/null; then
    print_error "Xcode command line tools not found!"
    print_error "Please install Xcode and the command line tools first."
    echo ""
    print_status "To install: xcode-select --install"
    print_status "Press any key to exit..."
    read -n 1
    exit 1
fi

XCODE_VERSION=$(xcodebuild -version | head -n 1)
print_success "Found $XCODE_VERSION"
echo ""

# Check Python and install pytubefix
print_step "Checking Python dependencies..."
if ! command -v python3 &> /dev/null; then
    print_error "Python 3 not found!"
    print_error "Please install Python 3 from https://python.org"
    echo ""
    print_status "Press any key to exit..."
    read -n 1
    exit 1
fi

PYTHON_VERSION=$(python3 --version)
print_success "Found $PYTHON_VERSION"

# Install pytubefix if not present
if ! python3 -c "import pytubefix" 2>/dev/null; then
    print_warning "pytubefix not found. Installing..."
    python3 -m pip install pytubefix>=9.4.1 || {
        print_error "Failed to install pytubefix."
        print_error "Please install it manually: pip3 install pytubefix"
        echo ""
        print_status "Press any key to exit..."
        read -n 1
        exit 1
    }
    print_success "pytubefix installed successfully"
else
    print_success "pytubefix is already installed"
fi
echo ""

# Clean previous builds
print_step "Cleaning previous builds..."
rm -rf "$BUILD_DIR"
mkdir -p "$BUILD_DIR"
print_success "Build directory cleaned"
echo ""

# Build the project
print_step "Building $PROJECT_NAME..."
echo ""
cd "$PROJECT_NAME"

# Show build progress
echo -e "${YELLOW}Building... This may take a few minutes.${NC}"
echo ""

# First try a regular build to catch any compilation errors
print_status "Checking for compilation errors..."
if ! xcodebuild \
    -project "$PROJECT_NAME.xcodeproj" \
    -scheme "$SCHEME_NAME" \
    -configuration "$CONFIGURATION" \
    build > /dev/null 2>&1; then

    print_error "Compilation failed! Showing detailed errors:"
    echo ""
    xcodebuild \
        -project "$PROJECT_NAME.xcodeproj" \
        -scheme "$SCHEME_NAME" \
        -configuration "$CONFIGURATION" \
        build 2>&1 | grep -A 3 -B 1 "error:" || echo "Build failed with unknown errors"
    echo ""
    print_status "Press any key to exit..."
    read -n 1
    exit 1
fi

print_success "Compilation successful! Creating archive..."

# Now create the archive
xcodebuild \
    -project "$PROJECT_NAME.xcodeproj" \
    -scheme "$SCHEME_NAME" \
    -configuration "$CONFIGURATION" \
    -derivedDataPath "../$DERIVED_DATA_PATH" \
    -archivePath "../$BUILD_DIR/$PROJECT_NAME.xcarchive" \
    archive \
    | grep -E "(▸|❌|⚠️|✅)" || true

if [ ${PIPESTATUS[0]} -ne 0 ]; then
    echo ""
    print_error "Archive creation failed! Please check the error messages above."
    echo ""
    print_status "Press any key to exit..."
    read -n 1
    exit 1
fi

echo ""
print_success "Build completed successfully!"
echo ""

# Export the app
print_step "Exporting application..."

# Create export options plist
cat > "../$BUILD_DIR/ExportOptions.plist" << EOF
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>method</key>
    <string>mac-application</string>
    <key>destination</key>
    <string>export</string>
</dict>
</plist>
EOF

xcodebuild \
    -exportArchive \
    -archivePath "../$BUILD_DIR/$PROJECT_NAME.xcarchive" \
    -exportPath "../$BUILD_DIR/Export" \
    -exportOptionsPlist "../$BUILD_DIR/ExportOptions.plist" \
    | grep -E "(▸|❌|⚠️|✅)" || true

if [ ${PIPESTATUS[0]} -ne 0 ]; then
    echo ""
    print_error "Export failed! Please check the error messages above."
    echo ""
    print_status "Press any key to exit..."
    read -n 1
    exit 1
fi

cd ..

print_success "Application exported successfully!"
echo ""

# Copy Python scripts to the app bundle
print_step "Copying Python scripts to app bundle..."
APP_PATH="$BUILD_DIR/Export/Scripture.app"
PYTHON_DEST="$APP_PATH/Contents/Resources"

if [ -d "$APP_PATH" ]; then
    if [ -d "$PROJECT_NAME/$PROJECT_NAME/Python" ]; then
        cp -r "$PROJECT_NAME/$PROJECT_NAME/Python/"* "$PYTHON_DEST/"
        chmod +x "$PYTHON_DEST"/*.py 2>/dev/null || true
        print_success "Python scripts copied and configured"
    else
        print_warning "Python scripts directory not found, but app will still work"
    fi
else
    print_error "App bundle not found at $APP_PATH"
    echo ""
    print_status "Press any key to exit..."
    read -n 1
    exit 1
fi
echo ""

# Create a distributable DMG
print_step "Creating distributable installer..."
DMG_NAME="Scripture-v1.0.dmg"
DMG_PATH="$BUILD_DIR/$DMG_NAME"

# Remove existing DMG
rm -f "$DMG_PATH"

# Create DMG
hdiutil create -volname "Scripture" -srcfolder "$BUILD_DIR/Export" -ov -format UDZO "$DMG_PATH" > /dev/null 2>&1

if [ $? -eq 0 ]; then
    print_success "DMG installer created successfully!"
else
    print_warning "DMG creation failed, but app is still available"
fi
echo ""

# Final success message
echo -e "${GREEN}╔══════════════════════════════════════════════════════════════╗${NC}"
echo -e "${GREEN}║${NC}                    ${CYAN}🎉 BUILD COMPLETED! 🎉${NC}                     ${GREEN}║${NC}"
echo -e "${GREEN}╚══════════════════════════════════════════════════════════════╝${NC}"
echo ""

print_success "Application built successfully!"
echo ""
echo -e "${CYAN}📦 Application:${NC} $APP_PATH"
if [ -f "$DMG_PATH" ]; then
    echo -e "${CYAN}💿 Installer:${NC} $DMG_PATH"
fi
echo ""

# Open the build folder
print_step "Opening build folder..."
open "$BUILD_DIR/Export"

echo ""
print_success "You can now run the application or distribute the DMG file!"
echo ""

# Auto-close countdown
echo -e "${YELLOW}Terminal will close automatically in 10 seconds...${NC}"
echo -e "${YELLOW}Press any key to close immediately.${NC}"

# Countdown with option to exit early
for i in {10..1}; do
    echo -ne "\rClosing in $i seconds... "
    read -t 1 -n 1 && break
done

echo ""
print_success "Build process completed! 🚀"

# Close terminal window
osascript -e 'tell application "Terminal" to close first window' 2>/dev/null || exit 0
