// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 56;
	objects = {

/* Begin PBXBuildFile section */
		A1234567890123456789012A /* YouTubeTranscriptDownloaderApp.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1234567890123456789012B /* YouTubeTranscriptDownloaderApp.swift */; };
		A1234567890123456789012C /* ContentView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1234567890123456789012D /* ContentView.swift */; };
		A1234567890123456789012E /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = A1234567890123456789012F /* Assets.xcassets */; };
		A1234567890123456789013A /* Preview Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = A1234567890123456789013B /* Preview Assets.xcassets */; };
		A1234567890123456789013E /* DownloadManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1234567890123456789013F /* DownloadManager.swift */; };
		A1234567890123456789023A /* download_transcript.py in Resources */ = {isa = PBXBuildFile; fileRef = A1234567890123456789023B /* download_transcript.py */; };
		A1234567890123456789024A /* install_dependencies.py in Resources */ = {isa = PBXBuildFile; fileRef = A1234567890123456789024B /* install_dependencies.py */; };
		A1234567890123456789025A /* requirements.txt in Resources */ = {isa = PBXBuildFile; fileRef = A1234567890123456789025B /* requirements.txt */; };
		A1234567890123456789026A /* get_channel_videos.py in Resources */ = {isa = PBXBuildFile; fileRef = A1234567890123456789026B /* get_channel_videos.py */; };
		A1234567890123456789027A /* get_channel_thumbnail.py in Resources */ = {isa = PBXBuildFile; fileRef = A1234567890123456789027B /* get_channel_thumbnail.py */; };
		A1234567890123456789028A /* download_transcript_enhanced.py in Resources */ = {isa = PBXBuildFile; fileRef = A1234567890123456789028B /* download_transcript_enhanced.py */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		A1234567890123456789012B /* YouTubeTranscriptDownloaderApp.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = YouTubeTranscriptDownloaderApp.swift; sourceTree = "<group>"; };
		A1234567890123456789012D /* ContentView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ContentView.swift; sourceTree = "<group>"; };
		A1234567890123456789012F /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		A1234567890123456789013B /* Preview Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = "Preview Assets.xcassets"; sourceTree = "<group>"; };
		A1234567890123456789013F /* DownloadManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DownloadManager.swift; sourceTree = "<group>"; };
		A1234567890123456789016A /* YouTubeTranscriptDownloader.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = YouTubeTranscriptDownloader.app; sourceTree = BUILT_PRODUCTS_DIR; };
		A1234567890123456789016B /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		A1234567890123456789023B /* download_transcript.py */ = {isa = PBXFileReference; lastKnownFileType = text.script.python; path = download_transcript.py; sourceTree = "<group>"; };
		A1234567890123456789024B /* install_dependencies.py */ = {isa = PBXFileReference; lastKnownFileType = text.script.python; path = install_dependencies.py; sourceTree = "<group>"; };
		A1234567890123456789025B /* requirements.txt */ = {isa = PBXFileReference; lastKnownFileType = text; path = requirements.txt; sourceTree = "<group>"; };
		A1234567890123456789026B /* get_channel_videos.py */ = {isa = PBXFileReference; lastKnownFileType = text.script.python; path = get_channel_videos.py; sourceTree = "<group>"; };
		A1234567890123456789027B /* get_channel_thumbnail.py */ = {isa = PBXFileReference; lastKnownFileType = text.script.python; path = get_channel_thumbnail.py; sourceTree = "<group>"; };
		A1234567890123456789028B /* download_transcript_enhanced.py */ = {isa = PBXFileReference; lastKnownFileType = text.script.python; path = download_transcript_enhanced.py; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		A1234567890123456789016C /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		A1234567890123456789016D /* YouTubeTranscriptDownloader */ = {
			isa = PBXGroup;
			children = (
				A1234567890123456789012B /* YouTubeTranscriptDownloaderApp.swift */,
				A1234567890123456789012D /* ContentView.swift */,
				A1234567890123456789017B /* Managers */,
				A1234567890123456789017C /* Python */,
				A1234567890123456789012F /* Assets.xcassets */,
				A1234567890123456789016B /* Info.plist */,
				A1234567890123456789017D /* Preview Content */,
			);
			path = YouTubeTranscriptDownloader;
			sourceTree = "<group>";
		};
		A1234567890123456789017B /* Managers */ = {
			isa = PBXGroup;
			children = (
				A1234567890123456789013F /* DownloadManager.swift */,
			);
			path = Managers;
			sourceTree = "<group>";
		};
		A1234567890123456789017C /* Python */ = {
			isa = PBXGroup;
			children = (
				A1234567890123456789023B /* download_transcript.py */,
				A1234567890123456789024B /* install_dependencies.py */,
				A1234567890123456789025B /* requirements.txt */,
				A1234567890123456789026B /* get_channel_videos.py */,
				A1234567890123456789027B /* get_channel_thumbnail.py */,
				A1234567890123456789028B /* download_transcript_enhanced.py */,
			);
			path = Python;
			sourceTree = "<group>";
		};
		A1234567890123456789017D /* Preview Content */ = {
			isa = PBXGroup;
			children = (
				A1234567890123456789013B /* Preview Assets.xcassets */,
			);
			path = "Preview Content";
			sourceTree = "<group>";
		};
		A1234567890123456789017E /* Products */ = {
			isa = PBXGroup;
			children = (
				A1234567890123456789016A /* YouTubeTranscriptDownloader.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		A1234567890123456789017F = {
			isa = PBXGroup;
			children = (
				A1234567890123456789016D /* YouTubeTranscriptDownloader */,
				A1234567890123456789017E /* Products */,
			);
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		A1234567890123456789018A /* YouTubeTranscriptDownloader */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = A1234567890123456789018B /* Build configuration list for PBXNativeTarget "YouTubeTranscriptDownloader" */;
			buildPhases = (
				A1234567890123456789018C /* Sources */,
				A1234567890123456789016C /* Frameworks */,
				A1234567890123456789018D /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = YouTubeTranscriptDownloader;
			productName = YouTubeTranscriptDownloader;
			productReference = A1234567890123456789016A /* YouTubeTranscriptDownloader.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		A1234567890123456789018E /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1500;
				LastUpgradeCheck = 1500;
				TargetAttributes = {
					A1234567890123456789018A = {
						CreatedOnToolsVersion = 15.0;
					};
				};
			};
			buildConfigurationList = A1234567890123456789018F /* Build configuration list for PBXProject "YouTubeTranscriptDownloader" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = A1234567890123456789017F;
			productRefGroup = A1234567890123456789017E /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				A1234567890123456789018A /* YouTubeTranscriptDownloader */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		A1234567890123456789018D /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A1234567890123456789013A /* Preview Assets.xcassets in Resources */,
				A1234567890123456789012E /* Assets.xcassets in Resources */,
				A1234567890123456789023A /* download_transcript.py in Resources */,
				A1234567890123456789024A /* install_dependencies.py in Resources */,
				A1234567890123456789025A /* requirements.txt in Resources */,
				A1234567890123456789026A /* get_channel_videos.py in Resources */,
				A1234567890123456789027A /* get_channel_thumbnail.py in Resources */,
				A1234567890123456789028A /* download_transcript_enhanced.py in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		A1234567890123456789018C /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A1234567890123456789012C /* ContentView.swift in Sources */,
				A1234567890123456789013E /* DownloadManager.swift in Sources */,
				A1234567890123456789012A /* YouTubeTranscriptDownloaderApp.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		A1234567890123456789019A /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 14.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = macosx;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		A1234567890123456789019B /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 14.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = macosx;
				SWIFT_COMPILATION_MODE = wholemodule;
			};
			name = Release;
		};
		A1234567890123456789019C /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = YouTubeTranscriptDownloader/YouTubeTranscriptDownloader.entitlements;
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"YouTubeTranscriptDownloader/Preview Content\"";
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.yourcompany.YouTubeTranscriptDownloader;
				PRODUCT_NAME = "Scripture";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
		A1234567890123456789019D /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = YouTubeTranscriptDownloader/YouTubeTranscriptDownloader.entitlements;
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"YouTubeTranscriptDownloader/Preview Content\"";
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.yourcompany.YouTubeTranscriptDownloader;
				PRODUCT_NAME = "Scripture";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		A1234567890123456789018B /* Build configuration list for PBXNativeTarget "YouTubeTranscriptDownloader" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A1234567890123456789019C /* Debug */,
				A1234567890123456789019D /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		A1234567890123456789018F /* Build configuration list for PBXProject "YouTubeTranscriptDownloader" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A1234567890123456789019A /* Debug */,
				A1234567890123456789019B /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = A1234567890123456789018E /* Project object */;
}
