#!/usr/bin/env python3
"""
Script to download YouTube video transcript using pytubefix
Usage: python3 download_transcript.py <video_url> <output_dir> <video_title>
"""

import sys
import json
import os
import re
from datetime import datetime

try:
    from pytubefix import YouTube
    PYTUBEFIX_AVAILABLE = True
except ImportError:
    PYTUBEFIX_AVAILABLE = False

def sanitize_filename(filename):
    """Remove invalid characters from filename"""
    # Remove invalid characters for file names
    filename = re.sub(r'[<>:"/\\|?*]', '', filename)
    # Replace multiple spaces with single space
    filename = re.sub(r'\s+', ' ', filename)
    # Trim whitespace
    filename = filename.strip()
    # Limit length
    if len(filename) > 200:
        filename = filename[:200]
    return filename

def download_transcript(video_url, output_dir, video_title, include_timestamps=True, preferred_language_codes=None):
    """Download transcript for a YouTube video with language preference support"""
    try:
        # Validate URL format first
        if not video_url or not video_url.startswith(('https://www.youtube.com/', 'https://youtu.be/', 'https://youtube.com/')):
            print(json.dumps({
                "error": f"Invalid YouTube URL format: {video_url}. Please use a direct video URL.",
                "success": False
            }))
            return 1

        # Check if URL contains a valid video ID pattern
        video_id_pattern = r'(?:v=|\/|embed\/|watch\?v=|youtu\.be\/)([0-9A-Za-z_-]{11})'
        if not re.search(video_id_pattern, video_url):
            print(json.dumps({
                "error": f"Invalid YouTube video URL: {video_url}. Please use a direct video URL like 'https://www.youtube.com/watch?v=VIDEO_ID'",
                "success": False
            }))
            return 1

        # Create output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)

        # Always create a working transcript file
        safe_title = sanitize_filename(video_title if video_title else "YouTube Video")
        transcript_file = os.path.join(output_dir, f"{safe_title}.txt")

        if PYTUBEFIX_AVAILABLE:
            try:
                # Try to get actual transcript using pytubefix
                yt = YouTube(video_url)

                # Get video information
                video_id = yt.video_id
                actual_title = yt.title
                author = yt.author

                # Get captions/transcript
                captions = yt.captions

                transcript_text = ""
                if captions:
                    # Try to get English captions first
                    caption = None
                    for lang_code in ['en', 'a.en', 'en-US', 'en-GB']:
                        if lang_code in captions:
                            caption = captions[lang_code]
                            break

                    if not caption:
                        # Get first available caption
                        caption = list(captions.values())[0]

                    # Download transcript
                    if include_timestamps:
                        transcript_text = caption.generate_srt_captions()
                    else:
                        # Get plain text without timestamps
                        transcript_text = ""
                        try:
                            # Get the raw transcript data
                            raw_transcript = caption.xml_captions
                            # Parse XML to extract text only
                            import xml.etree.ElementTree as ET
                            root = ET.fromstring(raw_transcript)
                            text_parts = []
                            for text_elem in root.findall('.//text'):
                                if text_elem.text:
                                    text_parts.append(text_elem.text.strip())
                            transcript_text = ' '.join(text_parts)
                        except Exception as e:
                            # Fallback to SRT and strip timestamps
                            srt_text = caption.generate_srt_captions()
                            lines = srt_text.split('\n')
                            text_lines = []
                            for line in lines:
                                # Skip sequence numbers and timestamp lines
                                if not line.strip().isdigit() and '-->' not in line and line.strip():
                                    text_lines.append(line.strip())
                            transcript_text = ' '.join(text_lines)
                else:
                    transcript_text = "No captions available for this video."

                # Save transcript
                with open(transcript_file, 'w', encoding='utf-8') as f:
                    f.write(f"YouTube Transcript\n")
                    f.write(f"==================\n\n")
                    f.write(f"Title: {actual_title}\n")
                    f.write(f"Author: {author}\n")
                    f.write(f"Video ID: {video_id}\n")
                    f.write(f"URL: {video_url}\n")
                    f.write(f"Downloaded: {datetime.now().isoformat()}\n\n")
                    f.write("Transcript:\n")
                    f.write("-----------\n")
                    f.write(transcript_text)

                result = {
                    "message": "Transcript downloaded successfully",
                    "output_path": transcript_file,
                    "video_title": actual_title,
                    "success": True
                }
                print(json.dumps(result))
                return 0

            except Exception as e:
                # Return detailed error information
                error_result = {
                    "error": f"pytubefix error: {str(e)}",
                    "success": False,
                    "suggestion": "Try installing dependencies or check if the video URL is valid"
                }
                print(json.dumps(error_result))
                return 1

        # If pytubefix is not available, return error
        if not PYTUBEFIX_AVAILABLE:
            error_result = {
                "error": "pytubefix not installed. Please install dependencies first.",
                "success": False,
                "suggestion": "Click 'Install Dependencies' button in the app"
            }
            print(json.dumps(error_result))
            return 1

        # If we reach here, something else went wrong
        error_result = {
            "error": "Unknown error occurred during transcript download",
            "success": False,
            "suggestion": "Check video URL and try again"
        }
        print(json.dumps(error_result))
        return 1

        # Create YouTube object
        yt = YouTube(video_url)
        
        # Get video information
        video_id = yt.video_id
        actual_title = yt.title
        author = yt.author
        
        # Use provided title or actual title
        title = video_title if video_title else actual_title
        
        # Create safe filename
        safe_title = sanitize_filename(title)
        
        # Check if transcript already exists
        transcript_file = os.path.join(output_dir, f"{safe_title}.txt")
        meta_file = os.path.join(output_dir, f"{safe_title}.meta")
        
        if os.path.exists(meta_file):
            # Check if this is the same video
            try:
                with open(meta_file, 'r', encoding='utf-8') as f:
                    meta_data = json.load(f)
                    if meta_data.get('video_id') == video_id:
                        result = {
                            "message": "Transcript already exists",
                            "output_path": transcript_file,
                            "skipped": True,
                            "success": True
                        }
                        print(json.dumps(result))
                        return 0
            except:
                pass  # Continue with download if meta file is corrupted
        
        # Get captions/transcript
        captions = yt.captions
        
        if not captions:
            error_result = {
                "error": "No captions available for this video",
                "success": False
            }
            print(json.dumps(error_result))
            return 1
        
        # Try to get captions based on preferred language, then fallback
        caption = None

        # Use preferred language codes if provided, otherwise default to English
        if preferred_language_codes:
            language_codes_to_try = preferred_language_codes
        else:
            language_codes_to_try = ['en', 'a.en', 'en-US', 'en-GB']

        # First try preferred language codes
        for lang_code in language_codes_to_try:
            if lang_code in captions:
                caption = captions[lang_code]
                print(f"Found caption in preferred language: {lang_code}", file=sys.stderr)
                break

        # If no preferred language found, try English as fallback (unless it was already tried)
        if not caption and preferred_language_codes and not any(code.startswith('en') for code in preferred_language_codes):
            for lang_code in ['en', 'a.en', 'en-US', 'en-GB']:
                if lang_code in captions:
                    caption = captions[lang_code]
                    print(f"Fallback to English caption: {lang_code}", file=sys.stderr)
                    break

        # If still no caption found, get first available
        if not caption:
            caption = list(captions.values())[0]
            print(f"Using first available caption: {caption.code if hasattr(caption, 'code') else 'unknown'}", file=sys.stderr)
        
        # Download transcript
        transcript_text = caption.generate_srt_captions()
        
        # Ensure output directory exists
        os.makedirs(output_dir, exist_ok=True)
        
        # Save transcript
        with open(transcript_file, 'w', encoding='utf-8') as f:
            f.write(transcript_text)
        
        # Save metadata
        meta_data = {
            "video_id": video_id,
            "title": actual_title,
            "author": author,
            "url": video_url,
            "download_date": datetime.now().isoformat(),
            "caption_language": caption.code if hasattr(caption, 'code') else 'unknown'
        }
        
        with open(meta_file, 'w', encoding='utf-8') as f:
            json.dump(meta_data, f, indent=2, ensure_ascii=False)
        
        # Return success
        result = {
            "message": "Transcript downloaded successfully",
            "output_path": transcript_file,
            "meta_path": meta_file,
            "video_title": actual_title,
            "success": True
        }
        
        print(json.dumps(result))
        return 0
        
    except Exception as e:
        error_result = {
            "error": str(e),
            "success": False
        }
        print(json.dumps(error_result))
        return 1

if __name__ == "__main__":
    if len(sys.argv) < 3:
        print(json.dumps({"error": "Video URL and output directory required", "success": False}))
        sys.exit(1)

    video_url = sys.argv[1]
    output_dir = sys.argv[2]
    video_title = sys.argv[3] if len(sys.argv) > 3 else ""
    include_timestamps = sys.argv[4].lower() == 'true' if len(sys.argv) > 4 else True

    # Parse preferred language codes (comma-separated list)
    preferred_language_codes = None
    if len(sys.argv) > 5 and sys.argv[5]:
        preferred_language_codes = [code.strip() for code in sys.argv[5].split(',') if code.strip()]

    sys.exit(download_transcript(video_url, output_dir, video_title, include_timestamps, preferred_language_codes))
