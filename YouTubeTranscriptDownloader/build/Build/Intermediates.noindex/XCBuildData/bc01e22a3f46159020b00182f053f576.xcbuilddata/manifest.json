{"client": {"name": "basic", "version": 0, "file-system": "device-agnostic", "perform-ownership-analysis": "no"}, "targets": {"": ["<all>"]}, "nodes": {"/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex": {"is-mutated": true}, "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/EagerLinkingTBDs/Debug": {"is-mutated": true}, "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products": {"is-mutated": true}, "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug": {"is-mutated": true}, "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app": {"is-mutated": true}, "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/MacOS/Scripture": {"is-mutated": true}, "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/MacOS/Scripture.debug.dylib": {"is-mutated": true}, "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/MacOS/__preview.dylib": {"is-mutated": true}, "<TRIGGER: CodeSign /Users/<USER>/Downloads/Apps/Scripture\\ 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app>": {"is-command-timestamp": true}, "<TRIGGER: Ld /Users/<USER>/Downloads/Apps/Scripture\\ 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/MacOS/Scripture normal>": {"is-command-timestamp": true}, "<TRIGGER: Ld /Users/<USER>/Downloads/Apps/Scripture\\ 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/MacOS/Scripture.debug.dylib normal>": {"is-command-timestamp": true}, "<TRIGGER: Ld /Users/<USER>/Downloads/Apps/Scripture\\ 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/MacOS/__preview.dylib normal>": {"is-command-timestamp": true}, "<TRIGGER: MkDir /Users/<USER>/Downloads/Apps/Scripture\\ 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app>": {"is-command-timestamp": true}, "<TRIGGER: Validate /Users/<USER>/Downloads/Apps/Scripture\\ 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app>": {"is-command-timestamp": true}}, "commands": {"<all>": {"tool": "phony", "inputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/EagerLinkingTBDs/Debug", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/MacOS", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/MacOS/__preview.dylib", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/_CodeSignature", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache", "<Linked Binary Debug Dylib /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/MacOS/Scripture.debug.dylib>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-scanning>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--end>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--linker-inputs-ready>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--modules-ready>", "<workspace-Debug-macosx15.5-macos--stale-file-removal>"], "outputs": ["<all>"]}, "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-Debug-macosx--arm64-build-headers-stale-file-removal>": {"tool": "stale-file-removal", "expectedOutputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/MacOS/Scripture", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/_CodeSignature", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/MacOS/Scripture.debug.dylib", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/MacOS/__preview.dylib", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/assetcatalog_output/thinned", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/assetcatalog_dependencies_thinned", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/assetcatalog_generated_info.plist_thinned", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/assetcatalog_output/unthinned", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/assetcatalog_dependencies_unthinned", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/assetcatalog_generated_info.plist_unthinned", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/Resources/download_transcript.py", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/Resources/download_transcript_enhanced.py", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/Resources/get_channel_thumbnail.py", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/Resources/get_channel_videos.py", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/Resources/install_dependencies.py", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/Resources/requirements.txt", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/DerivedSources/GeneratedAssetSymbols-Index.plist", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/assetcatalog_generated_info.plist", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/Resources/Assets.car", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/assetcatalog_signature", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/assetcatalog_output/thinned", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/assetcatalog_output/unthinned", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/MacOS", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/Resources", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/Info.plist", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/PkgInfo", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture.app.xcent", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture.app.xcent.der", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloader Swift Compilation Finished", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/DownloadManager.o", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloaderApp.o", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/DownloadManager.stringsdata", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloaderApp.stringsdata", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/ContentView.swiftconstvalues", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/DownloadManager.swiftconstvalues", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloaderApp.swiftconstvalues", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftconstvalues", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture-ExecutorLinkFileList-normal-arm64.txt", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.swiftmodule/Project/arm64-apple-macos.swiftsourceinfo", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.swiftmodule/arm64-apple-macos.abi.json", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.swiftmodule/arm64-apple-macos.swiftdoc", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.swiftmodule/arm64-apple-macos.swiftmodule", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/MacOS/Scripture", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/MacOS/Scripture.debug.dylib", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture_lto.o", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture_dependency_info.dat", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/MacOS/__preview.dylib", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloader Swift Compilation Requirements Finished", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.swiftmodule", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.swiftsourceinfo", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.abi.json", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture-Swift.h", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.swiftdoc", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/DerivedSources/Scripture-Swift.h", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/DerivedSources/Entitlements.plist", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.LinkFileList", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.SwiftConstValuesFileList", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.SwiftFileList", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloader-OutputFileMap.json", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloader_const_extract_protocols.json", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture-DebugDylibInstallName-normal-arm64.txt", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture-DebugDylibPath-normal-arm64.txt", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture-all-non-framework-target-headers.hmap", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture-all-target-headers.hmap", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture-generated-files.hmap", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture-own-target-headers.hmap", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture-project-headers.hmap", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture.DependencyMetadataFileList", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture.DependencyStaticMetadataFileList", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture.hmap", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/empty-Scripture.plist"], "roots": ["/tmp/YouTubeTranscriptDownloader.dst", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-Debug-macosx--arm64-build-headers-stale-file-removal>"]}, "<workspace-Debug-macosx15.5-macos--stale-file-removal>": {"tool": "stale-file-removal", "expectedOutputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60-VFS/all-product-headers.yaml"], "outputs": ["<workspace-Debug-macosx15.5-macos--stale-file-removal>"]}, "P0:::ClangStatCache /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache": {"tool": "shell", "description": "ClangStatCache /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache", "inputs": [], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache", "<ClangStatCache /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-o", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache"], "env": {}, "always-out-of-date": true, "working-directory": "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader.xcodeproj", "signature": "a027e5da761e499784cd02ca90a5e2db"}, "P0:::CreateBuildDirectory /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex", "inputs": [], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex>", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex"]}, "P0:::CreateBuildDirectory /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/EagerLinkingTBDs/Debug": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/EagerLinkingTBDs/Debug", "inputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex"], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/EagerLinkingTBDs/Debug>", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/EagerLinkingTBDs/Debug"]}, "P0:::CreateBuildDirectory /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products", "inputs": [], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products>", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products"]}, "P0:::CreateBuildDirectory /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug", "inputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products"], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug>", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug"]}, "P0:::Gate WorkspaceHeaderMapVFSFilesWritten": {"tool": "phony", "inputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60-VFS/all-product-headers.yaml"], "outputs": ["<WorkspaceHeaderMapVFSFilesWritten>"]}, "P0:::Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--AppIntentsMetadataTaskProducer": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>", "<ExtractAppIntentsMetadata /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/Resources/Metadata.appintents>", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.SwiftConstValuesFileList", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture.DependencyMetadataFileList", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture.DependencyStaticMetadataFileList"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--AppIntentsMetadataTaskProducer>"]}, "P0:::Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-ChangeAlternatePermissions": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-ChangePermissions>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--will-sign>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-ChangeAlternatePermissions>"]}, "P0:::Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-ChangePermissions": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-StripSymbols>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--will-sign>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-ChangePermissions>"]}, "P0:::Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-CodeSign": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-ChangeAlternatePermissions>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--will-sign>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>", "<CodeSign /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app>", "<CodeSign /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/MacOS/Scripture.debug.dylib>", "<CodeSign /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/MacOS/__preview.dylib>"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-CodeSign>"]}, "P0:::Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-CopyAside": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-GenerateStubAPI>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--will-sign>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-CopyAside>"]}, "P0:::Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-GenerateStubAPI": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ProductPostprocessingTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-GenerateStubAPI>"]}, "P0:::Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-RegisterExecutionPolicyException": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-CodeSign>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--will-sign>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>", "<RegisterExecutionPolicyException /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app>"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-RegisterExecutionPolicyException>"]}, "P0:::Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-RegisterProduct": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-Validate>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--will-sign>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>", "<LSRegisterURL /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app>", "<Touch /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app>"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-RegisterProduct>"]}, "P0:::Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-StripSymbols": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-CopyAside>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--will-sign>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-StripSymbols>"]}, "P0:::Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-Validate": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-RegisterExecutionPolicyException>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--will-sign>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>", "<Validate /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app>"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-Validate>"]}, "P0:::Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--CopySwiftPackageResourcesTaskProducer": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--CopySwiftPackageResourcesTaskProducer>"]}, "P0:::Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--CustomTaskProducer": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--CustomTaskProducer>"]}, "P0:::Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--DocumentationTaskProducer": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--DocumentationTaskProducer>"]}, "P0:::Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--GenerateAppPlaygroundAssetCatalogTaskProducer": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--GeneratedFilesTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--GenerateAppPlaygroundAssetCatalogTaskProducer>"]}, "P0:::Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--GeneratedFilesTaskProducer": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ProductStructureTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture.app.xcent", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture.app.xcent.der", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/DerivedSources/Entitlements.plist"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--GeneratedFilesTaskProducer>"]}, "P0:::Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--HeadermapTaskProducer": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--GenerateAppPlaygroundAssetCatalogTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture-all-non-framework-target-headers.hmap", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture-all-target-headers.hmap", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture-generated-files.hmap", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture-own-target-headers.hmap", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture-project-headers.hmap", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture.hmap"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--HeadermapTaskProducer>"]}, "P0:::Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--InfoPlistTaskProducer": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/Info.plist", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/PkgInfo", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/empty-Scripture.plist"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--InfoPlistTaskProducer>"]}, "P0:::Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleMapTaskProducer": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleMapTaskProducer>"]}, "P0:::Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--RealityAssetsTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer>"]}, "P0:::Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ProductPostprocessingTaskProducer": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleMapTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--SwiftPackageCopyFilesTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--InfoPlistTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--SanitizerTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--SwiftStandardLibrariesTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--SwiftFrameworkABICheckerTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--SwiftABIBaselineGenerationTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--TestTargetTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--TestHostTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--CopySwiftPackageResourcesTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--TAPISymbolExtractorTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--DocumentationTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--CustomTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--StubBinaryTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--AppIntentsMetadataTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ProductPostprocessingTaskProducer>"]}, "P0:::Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ProductStructureTaskProducer": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--start>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>", "<MkDir /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app>", "<MkDir /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents>", "<MkDir /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/MacOS>", "<MkDir /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/Resources>"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ProductStructureTaskProducer>"]}, "P0:::Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--RealityAssetsTaskProducer": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--HeadermapTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--RealityAssetsTaskProducer>"]}, "P0:::Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--SanitizerTaskProducer": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--SanitizerTaskProducer>"]}, "P0:::Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--StubBinaryTaskProducer": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--StubBinaryTaskProducer>"]}, "P0:::Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--SwiftABIBaselineGenerationTaskProducer": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--SwiftABIBaselineGenerationTaskProducer>"]}, "P0:::Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--SwiftFrameworkABICheckerTaskProducer": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--SwiftFrameworkABICheckerTaskProducer>"]}, "P0:::Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--SwiftPackageCopyFilesTaskProducer": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--SwiftPackageCopyFilesTaskProducer>"]}, "P0:::Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--SwiftStandardLibrariesTaskProducer": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>", "<CopySwiftStdlib /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app>"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--SwiftStandardLibrariesTaskProducer>"]}, "P0:::Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--TAPISymbolExtractorTaskProducer": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--TAPISymbolExtractorTaskProducer>"]}, "P0:::Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--TestHostTaskProducer": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--TestHostTaskProducer>"]}, "P0:::Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--TestTargetPostprocessingTaskProducer": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ProductPostprocessingTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--TestTargetPostprocessingTaskProducer>"]}, "P0:::Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--TestTargetTaskProducer": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--TestTargetTaskProducer>"]}, "P0:::Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--copy-headers-completion": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/DerivedSources/GeneratedAssetSymbols-Index.plist"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--copy-headers-completion>"]}, "P0:::Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--fused-phase0-compile-sources&link-binary&copy-bundle-resources": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/assetcatalog_output/thinned/", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/assetcatalog_dependencies_thinned", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/assetcatalog_generated_info.plist_thinned", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/assetcatalog_output/unthinned/", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/assetcatalog_dependencies_unthinned", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/assetcatalog_generated_info.plist_unthinned", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/Resources/download_transcript.py", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/Resources/download_transcript_enhanced.py", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/Resources/get_channel_thumbnail.py", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/Resources/get_channel_videos.py", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/Resources/install_dependencies.py", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/Resources/requirements.txt", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/DerivedSources/GeneratedAssetSymbols-Index.plist", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/assetcatalog_generated_info.plist", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/Resources/Assets.car", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/assetcatalog_signature", "<MkDir /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/assetcatalog_output/thinned>", "<MkDir /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/assetcatalog_output/unthinned>", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloader Swift Compilation Finished", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/DownloadManager.o", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloaderApp.o", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/DownloadManager.stringsdata", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloaderApp.stringsdata", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/ContentView.swiftconstvalues", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/DownloadManager.swiftconstvalues", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloaderApp.swiftconstvalues", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftconstvalues", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture-ExecutorLinkFileList-normal-arm64.txt", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.swiftmodule/Project/arm64-apple-macos.swiftsourceinfo", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.swiftmodule/arm64-apple-macos.abi.json", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.swiftmodule/arm64-apple-macos.swiftdoc", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.swiftmodule/arm64-apple-macos.swiftmodule", "<Linked Binary /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/MacOS/Scripture>", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture_lto.o", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture_dependency_info.dat", "<Linked Binary Preview Injection Dylib /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/MacOS/__preview.dylib>", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloader Swift Compilation Requirements Finished", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.swiftmodule", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.swiftsourceinfo", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.abi.json", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture-Swift.h", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.swiftdoc", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.LinkFileList", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.SwiftFileList", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloader-OutputFileMap.json", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloader_const_extract_protocols.json", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture-DebugDylibInstallName-normal-arm64.txt", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture-DebugDylibPath-normal-arm64.txt"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--fused-phase0-compile-sources&link-binary&copy-bundle-resources>"]}, "P0:::Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--generated-headers": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/DerivedSources/GeneratedAssetSymbols-Index.plist"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--generated-headers>"]}, "P0:::Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--swift-generated-headers": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloader Swift Compilation Requirements Finished", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.swiftmodule", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.swiftsourceinfo", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.abi.json", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture-Swift.h", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.swiftdoc", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/DerivedSources/Scripture-Swift.h"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--swift-generated-headers>"]}, "P0:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Debug:CodeSign /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app": {"tool": "code-sign-task", "description": "CodeSign /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app", "inputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Assets.xcassets/", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/ContentView.swift/", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Managers/DownloadManager.swift/", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Preview Content/Preview Assets.xcassets/", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Python/download_transcript.py/", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Python/download_transcript_enhanced.py/", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Python/get_channel_thumbnail.py/", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Python/get_channel_videos.py/", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Python/install_dependencies.py/", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Python/requirements.txt/", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/YouTubeTranscriptDownloaderApp.swift/", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture.app.xcent/", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/Info.plist/", "<CodeSign /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/MacOS/Scripture.debug.dylib>", "<CodeSign /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/MacOS/__preview.dylib>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-ChangeAlternatePermissions>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--will-sign>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--entry>", "<TRIGGER: Ld /Users/<USER>/Downloads/Apps/Scripture\\ 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/MacOS/Scripture normal>", "<TRIGGER: MkDir /Users/<USER>/Downloads/Apps/Scripture\\ 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/_CodeSignature", "<CodeSign /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app>", "<TRIGGER: CodeSign /Users/<USER>/Downloads/Apps/Scripture\\ 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app>"]}, "P0:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Debug:CodeSign /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/MacOS/Scripture.debug.dylib": {"tool": "code-sign-task", "description": "CodeSign /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/MacOS/Scripture.debug.dylib", "inputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Assets.xcassets/", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/ContentView.swift/", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Managers/DownloadManager.swift/", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Preview Content/Preview Assets.xcassets/", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Python/download_transcript.py/", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Python/download_transcript_enhanced.py/", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Python/get_channel_thumbnail.py/", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Python/get_channel_videos.py/", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Python/install_dependencies.py/", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Python/requirements.txt/", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/YouTubeTranscriptDownloaderApp.swift/", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/Info.plist/", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-ChangeAlternatePermissions>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--will-sign>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--entry>", "<TRIGGER: Ld /Users/<USER>/Downloads/Apps/Scripture\\ 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/MacOS/Scripture.debug.dylib normal>"], "outputs": ["<CodeSign /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/MacOS/Scripture.debug.dylib>"]}, "P0:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Debug:CodeSign /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/MacOS/__preview.dylib": {"tool": "code-sign-task", "description": "CodeSign /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/MacOS/__preview.dylib", "inputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Assets.xcassets/", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/ContentView.swift/", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Managers/DownloadManager.swift/", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Preview Content/Preview Assets.xcassets/", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Python/download_transcript.py/", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Python/download_transcript_enhanced.py/", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Python/get_channel_thumbnail.py/", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Python/get_channel_videos.py/", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Python/install_dependencies.py/", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Python/requirements.txt/", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/YouTubeTranscriptDownloaderApp.swift/", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/Info.plist/", "<CodeSign /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/MacOS/Scripture.debug.dylib>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-ChangeAlternatePermissions>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--will-sign>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--entry>", "<TRIGGER: Ld /Users/<USER>/Downloads/Apps/Scripture\\ 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/MacOS/__preview.dylib normal>"], "outputs": ["<CodeSign /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/MacOS/__preview.dylib>"]}, "P0:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Debug:CompileAssetCatalogVariant thinned /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/Resources /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Preview Content/Preview Assets.xcassets /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Assets.xcassets": {"tool": "shell", "description": "CompileAssetCatalogVariant thinned /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/Resources /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Preview Content/Preview Assets.xcassets /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Assets.xcassets", "inputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Preview Content/Preview Assets.xcassets/", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Assets.xcassets/", "<MkDir /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/assetcatalog_output/thinned>", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/assetcatalog_output/thinned", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/assetcatalog_output/thinned/", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/assetcatalog_dependencies_thinned", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/assetcatalog_generated_info.plist_thinned"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/actool", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Preview Content/Preview Assets.xcassets", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Assets.xcassets", "--compile", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/assetcatalog_output/thinned", "--output-format", "human-readable-text", "--notices", "--warnings", "--export-dependency-info", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/assetcatalog_dependencies_thinned", "--output-partial-info-plist", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/assetcatalog_generated_info.plist_thinned", "--app-icon", "AppIcon", "--accent-color", "AccentColor", "--enable-on-demand-resources", "NO", "--development-region", "en", "--target-device", "mac", "--minimum-deployment-target", "14.0", "--platform", "macosx"], "env": {}, "working-directory": "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader", "control-enabled": false, "deps": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/assetcatalog_dependencies_thinned"], "deps-style": "dependency-info", "signature": "8235721ca07ac86ba416a5f7be0b3c0b"}, "P0:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Debug:CompileAssetCatalogVariant unthinned /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/Resources /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Preview Content/Preview Assets.xcassets /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Assets.xcassets": {"tool": "shell", "description": "CompileAssetCatalogVariant unthinned /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/Resources /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Preview Content/Preview Assets.xcassets /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Assets.xcassets", "inputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Preview Content/Preview Assets.xcassets/", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Assets.xcassets/", "<MkDir /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/assetcatalog_output/unthinned>", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/assetcatalog_output/unthinned", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/assetcatalog_output/unthinned/", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/assetcatalog_dependencies_unthinned", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/assetcatalog_generated_info.plist_unthinned"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/actool", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Preview Content/Preview Assets.xcassets", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Assets.xcassets", "--compile", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/assetcatalog_output/unthinned", "--output-format", "human-readable-text", "--notices", "--warnings", "--export-dependency-info", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/assetcatalog_dependencies_unthinned", "--output-partial-info-plist", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/assetcatalog_generated_info.plist_unthinned", "--app-icon", "AppIcon", "--accent-color", "AccentColor", "--enable-on-demand-resources", "NO", "--development-region", "en", "--target-device", "mac", "--minimum-deployment-target", "14.0", "--platform", "macosx"], "env": {}, "working-directory": "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader", "control-enabled": false, "deps": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/assetcatalog_dependencies_unthinned"], "deps-style": "dependency-info", "signature": "2985ad3a73c4e34d64d9641c91a119a0"}, "P0:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Debug:CopySwiftLibs /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app": {"tool": "embed-swift-stdlib", "description": "CopySwiftLibs /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app", "inputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/MacOS/Scripture.debug.dylib", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--immediate>"], "outputs": ["<CopySwiftStdlib /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app>"], "deps": "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/SwiftStdLibToolInputDependencies.dep"}, "P0:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Debug:CpResource /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/Resources/download_transcript.py /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Python/download_transcript.py": {"tool": "file-copy", "description": "CpResource /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/Resources/download_transcript.py /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Python/download_transcript.py", "inputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Python/download_transcript.py/", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--entry>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/Resources/download_transcript.py"]}, "P0:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Debug:CpResource /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/Resources/download_transcript_enhanced.py /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Python/download_transcript_enhanced.py": {"tool": "file-copy", "description": "CpResource /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/Resources/download_transcript_enhanced.py /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Python/download_transcript_enhanced.py", "inputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Python/download_transcript_enhanced.py/", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--entry>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/Resources/download_transcript_enhanced.py"]}, "P0:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Debug:CpResource /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/Resources/get_channel_thumbnail.py /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Python/get_channel_thumbnail.py": {"tool": "file-copy", "description": "CpResource /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/Resources/get_channel_thumbnail.py /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Python/get_channel_thumbnail.py", "inputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Python/get_channel_thumbnail.py/", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--entry>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/Resources/get_channel_thumbnail.py"]}, "P0:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Debug:CpResource /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/Resources/get_channel_videos.py /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Python/get_channel_videos.py": {"tool": "file-copy", "description": "CpResource /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/Resources/get_channel_videos.py /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Python/get_channel_videos.py", "inputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Python/get_channel_videos.py/", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--entry>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/Resources/get_channel_videos.py"]}, "P0:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Debug:CpResource /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/Resources/install_dependencies.py /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Python/install_dependencies.py": {"tool": "file-copy", "description": "CpResource /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/Resources/install_dependencies.py /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Python/install_dependencies.py", "inputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Python/install_dependencies.py/", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--entry>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/Resources/install_dependencies.py"]}, "P0:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Debug:CpResource /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/Resources/requirements.txt /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Python/requirements.txt": {"tool": "file-copy", "description": "CpResource /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/Resources/requirements.txt /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Python/requirements.txt", "inputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Python/requirements.txt/", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--entry>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/Resources/requirements.txt"]}, "P0:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Debug:ExtractAppIntentsMetadata": {"tool": "shell", "description": "ExtractAppIntentsMetadata", "inputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/ContentView.swift", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Managers/DownloadManager.swift", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/YouTubeTranscriptDownloaderApp.swift", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/ContentView.swiftconstvalues", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/DownloadManager.swiftconstvalues", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloaderApp.swiftconstvalues", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftconstvalues", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/MacOS/Scripture", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture.DependencyMetadataFileList", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture.DependencyStaticMetadataFileList", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture_dependency_info.dat", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.SwiftFileList", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.SwiftConstValuesFileList", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--entry>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata", "<ExtractAppIntentsMetadata /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/Resources/Metadata.appintents>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/appintentsmetadataprocessor", "--toolchain-dir", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain", "--module-name", "Scripture", "--sdk-root", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "--xcode-version", "16F6", "--platform-family", "macOS", "--deployment-target", "14.0", "--bundle-identifier", "com.yourcompany.YouTubeTranscriptDownloader", "--output", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/Resources", "--target-triple", "arm64-apple-macos14.0", "--binary-file", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/MacOS/Scripture", "--dependency-file", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture_dependency_info.dat", "--stringsdata-file", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata", "--source-file-list", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.SwiftFileList", "--metadata-file-list", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture.DependencyMetadataFileList", "--static-metadata-file-list", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture.DependencyStaticMetadataFileList", "--swift-const-vals-list", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.SwiftConstValuesFileList", "--compile-time-extraction", "--deployment-aware-processing", "--validate-assistant-intents", "--no-app-shortcuts-localization"], "env": {}, "working-directory": "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader", "signature": "3c500a6c196a3bf853cc8b39f4246cf0"}, "P0:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Debug:Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-Debug-macosx--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/YouTubeTranscriptDownloader.dst>", "<CreateBuildDirectory-/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug>", "<CreateBuildDirectory-/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/EagerLinkingTBDs/Debug>"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>"]}, "P0:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Debug:Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-linking": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-Debug-macosx--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/YouTubeTranscriptDownloader.dst>", "<CreateBuildDirectory-/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug>", "<CreateBuildDirectory-/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/EagerLinkingTBDs/Debug>"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-linking>"]}, "P0:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Debug:Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-scanning": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-Debug-macosx--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/YouTubeTranscriptDownloader.dst>", "<CreateBuildDirectory-/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug>", "<CreateBuildDirectory-/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/EagerLinkingTBDs/Debug>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-scanning>"]}, "P0:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Debug:Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--end": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--entry>", "<CodeSign /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app>", "<CodeSign /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/MacOS/Scripture.debug.dylib>", "<CodeSign /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/MacOS/__preview.dylib>", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/assetcatalog_output/thinned/", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/assetcatalog_dependencies_thinned", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/assetcatalog_generated_info.plist_thinned", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/assetcatalog_output/unthinned/", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/assetcatalog_dependencies_unthinned", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/assetcatalog_generated_info.plist_unthinned", "<CopySwiftStdlib /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app>", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/Resources/download_transcript.py", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/Resources/download_transcript_enhanced.py", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/Resources/get_channel_thumbnail.py", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/Resources/get_channel_videos.py", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/Resources/install_dependencies.py", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/Resources/requirements.txt", "<ExtractAppIntentsMetadata /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/Resources/Metadata.appintents>", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/DerivedSources/GeneratedAssetSymbols-Index.plist", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/assetcatalog_generated_info.plist", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/Resources/Assets.car", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/assetcatalog_signature", "<MkDir /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/assetcatalog_output/thinned>", "<MkDir /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/assetcatalog_output/unthinned>", "<MkDir /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app>", "<MkDir /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents>", "<MkDir /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/MacOS>", "<MkDir /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/Resources>", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/Info.plist", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/PkgInfo", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture.app.xcent", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture.app.xcent.der", "<RegisterExecutionPolicyException /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app>", "<LSRegisterURL /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app>", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloader Swift Compilation Finished", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/DownloadManager.o", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloaderApp.o", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/DownloadManager.stringsdata", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloaderApp.stringsdata", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/ContentView.swiftconstvalues", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/DownloadManager.swiftconstvalues", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloaderApp.swiftconstvalues", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftconstvalues", "<Touch /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app>", "<Validate /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app>", "<ValidateDevelopmentAssets-/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build>", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture-ExecutorLinkFileList-normal-arm64.txt", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.swiftmodule/Project/arm64-apple-macos.swiftsourceinfo", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.swiftmodule/arm64-apple-macos.abi.json", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.swiftmodule/arm64-apple-macos.swiftdoc", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.swiftmodule/arm64-apple-macos.swiftmodule", "<Linked Binary /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/MacOS/Scripture>", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture_lto.o", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture_dependency_info.dat", "<Linked Binary Preview Injection Dylib /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/MacOS/__preview.dylib>", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloader Swift Compilation Requirements Finished", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.swiftmodule", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.swiftsourceinfo", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.abi.json", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture-Swift.h", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.swiftdoc", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/DerivedSources/Scripture-Swift.h", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/DerivedSources/Scripture-Swift.h", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/DerivedSources/Entitlements.plist", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.LinkFileList", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.SwiftConstValuesFileList", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.SwiftFileList", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloader-OutputFileMap.json", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloader_const_extract_protocols.json", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture-DebugDylibInstallName-normal-arm64.txt", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture-DebugDylibPath-normal-arm64.txt", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture-all-non-framework-target-headers.hmap", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture-all-target-headers.hmap", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture-generated-files.hmap", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture-own-target-headers.hmap", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture-project-headers.hmap", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture.DependencyMetadataFileList", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture.DependencyStaticMetadataFileList", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture.hmap", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/empty-Scripture.plist", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--AppIntentsMetadataTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-ChangeAlternatePermissions>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-ChangePermissions>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-CodeSign>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-CopyAside>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-GenerateStubAPI>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-RegisterExecutionPolicyException>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-RegisterProduct>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-StripSymbols>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-Validate>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--CopySwiftPackageResourcesTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--CustomTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--DocumentationTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--GenerateAppPlaygroundAssetCatalogTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--GeneratedFilesTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--HeadermapTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--InfoPlistTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleMapTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ProductPostprocessingTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ProductStructureTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--RealityAssetsTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--SanitizerTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--StubBinaryTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--SwiftABIBaselineGenerationTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--SwiftFrameworkABICheckerTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--SwiftPackageCopyFilesTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--SwiftStandardLibrariesTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--TAPISymbolExtractorTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--TestHostTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--TestTargetPostprocessingTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--TestTargetTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--copy-headers-completion>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--generated-headers>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--swift-generated-headers>"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--end>"]}, "P0:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Debug:Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--entry": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-Debug-macosx--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/YouTubeTranscriptDownloader.dst>", "<CreateBuildDirectory-/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug>", "<CreateBuildDirectory-/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/EagerLinkingTBDs/Debug>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--entry>"]}, "P0:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Debug:Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--immediate": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-Debug-macosx--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/YouTubeTranscriptDownloader.dst>", "<CreateBuildDirectory-/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug>", "<CreateBuildDirectory-/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/EagerLinkingTBDs/Debug>"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--immediate>"]}, "P0:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Debug:Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--linker-inputs-ready": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture-ExecutorLinkFileList-normal-arm64.txt", "<Linked Binary /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/MacOS/Scripture>", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture_lto.o", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture_dependency_info.dat", "<Linked Binary Preview Injection Dylib /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/MacOS/__preview.dylib>", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloader Swift Compilation Requirements Finished", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.swiftmodule", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.swiftsourceinfo", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.abi.json", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture-Swift.h", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.swiftdoc", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.LinkFileList", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture-DebugDylibInstallName-normal-arm64.txt", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture-DebugDylibPath-normal-arm64.txt"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--linker-inputs-ready>"]}, "P0:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Debug:Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--modules-ready": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.swiftmodule/Project/arm64-apple-macos.swiftsourceinfo", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.swiftmodule/arm64-apple-macos.abi.json", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.swiftmodule/arm64-apple-macos.swiftdoc", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.swiftmodule/arm64-apple-macos.swiftmodule", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloader Swift Compilation Requirements Finished", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.swiftmodule", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.swiftsourceinfo", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.abi.json", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture-Swift.h", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.swiftdoc", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/DerivedSources/Scripture-Swift.h"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--modules-ready>"]}, "P0:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Debug:Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--unsigned-product-ready": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/assetcatalog_output/thinned/", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/assetcatalog_dependencies_thinned", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/assetcatalog_generated_info.plist_thinned", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/assetcatalog_output/unthinned/", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/assetcatalog_dependencies_unthinned", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/assetcatalog_generated_info.plist_unthinned", "<CopySwiftStdlib /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app>", "<ExtractAppIntentsMetadata /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/Resources/Metadata.appintents>", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/DerivedSources/GeneratedAssetSymbols-Index.plist", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/assetcatalog_generated_info.plist", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/Resources/Assets.car", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/assetcatalog_signature", "<MkDir /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/assetcatalog_output/thinned>", "<MkDir /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/assetcatalog_output/unthinned>", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture.app.xcent", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture.app.xcent.der", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloader Swift Compilation Finished", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/DownloadManager.o", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloaderApp.o", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/DownloadManager.stringsdata", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloaderApp.stringsdata", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/ContentView.swiftconstvalues", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/DownloadManager.swiftconstvalues", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloaderApp.swiftconstvalues", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftconstvalues", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture-ExecutorLinkFileList-normal-arm64.txt", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.swiftmodule/Project/arm64-apple-macos.swiftsourceinfo", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.swiftmodule/arm64-apple-macos.abi.json", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.swiftmodule/arm64-apple-macos.swiftdoc", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.swiftmodule/arm64-apple-macos.swiftmodule", "<Linked Binary /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/MacOS/Scripture>", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture_lto.o", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture_dependency_info.dat", "<Linked Binary Preview Injection Dylib /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/MacOS/__preview.dylib>", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloader Swift Compilation Requirements Finished", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.swiftmodule", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.swiftsourceinfo", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.abi.json", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture-Swift.h", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.swiftdoc", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/DerivedSources/Scripture-Swift.h", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/DerivedSources/Entitlements.plist", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.LinkFileList", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.SwiftConstValuesFileList", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.SwiftFileList", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloader-OutputFileMap.json", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloader_const_extract_protocols.json", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture-DebugDylibInstallName-normal-arm64.txt", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture-DebugDylibPath-normal-arm64.txt", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture.DependencyMetadataFileList", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture.DependencyStaticMetadataFileList", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-GenerateStubAPI>"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--unsigned-product-ready>"]}, "P0:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Debug:Gate target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--will-sign": {"tool": "phony", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--unsigned-product-ready>"], "outputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--will-sign>"]}, "P0:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Debug:GenerateAssetSymbols /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Preview Content/Preview Assets.xcassets /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Assets.xcassets": {"tool": "shell", "description": "GenerateAssetSymbols /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Preview Content/Preview Assets.xcassets /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Assets.xcassets", "inputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Preview Content/Preview Assets.xcassets/", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Assets.xcassets/", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/DerivedSources/GeneratedAssetSymbols-Index.plist"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/actool", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Preview Content/Preview Assets.xcassets", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Assets.xcassets", "--compile", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/Resources", "--output-format", "human-readable-text", "--notices", "--warnings", "--export-dependency-info", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/assetcatalog_dependencies", "--output-partial-info-plist", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/assetcatalog_generated_info.plist", "--app-icon", "AppIcon", "--accent-color", "AccentColor", "--enable-on-demand-resources", "NO", "--development-region", "en", "--target-device", "mac", "--minimum-deployment-target", "14.0", "--platform", "macosx", "--bundle-identifier", "com.yourcompany.YouTubeTranscriptDownloader", "--generate-swift-asset-symbols", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/DerivedSources/GeneratedAssetSymbols.swift", "--generate-objc-asset-symbols", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/DerivedSources/GeneratedAssetSymbols.h", "--generate-asset-symbol-index", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/DerivedSources/GeneratedAssetSymbols-Index.plist"], "env": {}, "working-directory": "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader", "control-enabled": false, "signature": "95070a2a2c753cc96044b54a1689389f"}, "P0:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Debug:LinkAssetCatalog /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Preview Content/Preview Assets.xcassets /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Assets.xcassets": {"tool": "link-assetcatalog", "description": "LinkAssetCatalog /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Preview Content/Preview Assets.xcassets /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Assets.xcassets", "inputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Preview Content/Preview Assets.xcassets/", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Assets.xcassets/", "<MkDir /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/Resources>", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/Resources", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/assetcatalog_output/thinned/", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/assetcatalog_output/unthinned/", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/assetcatalog_signature", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/assetcatalog_generated_info.plist", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/Resources/Assets.car"], "deps": "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/assetcatalog_dependencies"}, "P0:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Debug:LinkAssetCatalogSignature": {"tool": "link-assetcatalog", "description": "LinkAssetCatalogSignature", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/assetcatalog_signature"], "always-out-of-date": true}, "P0:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Debug:MkDir /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/assetcatalog_output/thinned": {"tool": "mkdir", "description": "MkDir /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/assetcatalog_output/thinned", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/assetcatalog_output/thinned", "<MkDir /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/assetcatalog_output/thinned>"]}, "P0:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Debug:MkDir /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/assetcatalog_output/unthinned": {"tool": "mkdir", "description": "MkDir /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/assetcatalog_output/unthinned", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/assetcatalog_output/unthinned", "<MkDir /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/assetcatalog_output/unthinned>"]}, "P0:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Debug:MkDir /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app": {"tool": "mkdir", "description": "MkDir /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--start>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--immediate>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app", "<MkDir /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app>", "<TRIGGER: MkDir /Users/<USER>/Downloads/Apps/Scripture\\ 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app>"]}, "P0:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Debug:MkDir /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents": {"tool": "mkdir", "description": "MkDir /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--start>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--immediate>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents", "<MkDir /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents>"]}, "P0:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Debug:MkDir /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/MacOS": {"tool": "mkdir", "description": "MkDir /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/MacOS", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--start>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--immediate>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/MacOS", "<MkDir /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/MacOS>"]}, "P0:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Debug:MkDir /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/Resources": {"tool": "mkdir", "description": "MkDir /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/Resources", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--start>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--immediate>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/Resources", "<MkDir /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/Resources>"]}, "P0:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Debug:ProcessInfoPlistFile /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/Info.plist /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/empty-Scripture.plist": {"tool": "info-plist-processor", "description": "ProcessInfoPlistFile /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/Info.plist /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/empty-Scripture.plist", "inputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/empty-Scripture.plist", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/assetcatalog_generated_info.plist", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--entry>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/Info.plist", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/PkgInfo"]}, "P0:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Debug:ProcessProductPackaging /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader.entitlements /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture.app.xcent": {"tool": "process-product-entitlements", "description": "ProcessProductPackaging /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader.entitlements /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture.app.xcent", "inputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader.entitlements", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/DerivedSources/Entitlements.plist", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ProductStructureTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--immediate>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture.app.xcent"]}, "P0:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Debug:ProcessProductPackagingDER /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture.app.xcent /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture.app.xcent.der": {"tool": "shell", "description": "ProcessProductPackagingDER /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture.app.xcent /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture.app.xcent.der", "inputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture.app.xcent", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ProductStructureTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--immediate>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture.app.xcent.der"], "args": ["/usr/bin/derq", "query", "-f", "xml", "-i", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture.app.xcent", "-o", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture.app.xcent.der", "--raw"], "env": {}, "working-directory": "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader", "signature": "acdd62c515cae02eee81d6eff2897095"}, "P0:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Debug:RegisterExecutionPolicyException /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app": {"tool": "register-execution-policy-exception", "description": "RegisterExecutionPolicyException /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app", "inputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-CodeSign>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--will-sign>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--entry>"], "outputs": ["<RegisterExecutionPolicyException /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app>"]}, "P0:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Debug:RegisterWithLaunchServices /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app": {"tool": "lsregisterurl", "description": "RegisterWithLaunchServices /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-Validate>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--will-sign>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--entry>", "<TRIGGER: Validate /Users/<USER>/Downloads/Apps/Scripture\\ 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app>"], "outputs": ["<LSRegisterURL /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app>"]}, "P0:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Debug:SwiftDriver Compilation YouTubeTranscriptDownloader normal arm64 com.apple.xcode.tools.swift.compiler": {"tool": "swift-driver-compilation", "description": "SwiftDriver Compilation YouTubeTranscriptDownloader normal arm64 com.apple.xcode.tools.swift.compiler", "inputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/ContentView.swift", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Managers/DownloadManager.swift", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/YouTubeTranscriptDownloaderApp.swift", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.SwiftFileList", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloader-OutputFileMap.json", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloader_const_extract_protocols.json", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture-generated-files.hmap", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture-own-target-headers.hmap", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture-all-target-headers.hmap", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture-project-headers.hmap", "<ClangStatCache /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--generated-headers>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--copy-headers-completion>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloader Swift Compilation Finished", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/DownloadManager.o", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloaderApp.o", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/DownloadManager.stringsdata", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloaderApp.stringsdata", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/ContentView.swiftconstvalues", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/DownloadManager.swiftconstvalues", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloaderApp.swiftconstvalues", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftconstvalues"]}, "P0:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Debug:Touch /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app": {"tool": "shell", "description": "Touch /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app", "inputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-Validate>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--will-sign>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--entry>"], "outputs": ["<Touch /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app>"], "args": ["/usr/bin/touch", "-c", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app"], "env": {}, "working-directory": "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader", "signature": "f34aa372c546a457a010999bb93c6534"}, "P0:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Debug:Validate /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app": {"tool": "validate-product", "description": "Validate /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app", "inputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/Info.plist", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--Barrier-RegisterExecutionPolicyException>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--will-sign>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--entry>", "<TRIGGER: CodeSign /Users/<USER>/Downloads/Apps/Scripture\\ 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app>"], "outputs": ["<Validate /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app>", "<TRIGGER: Validate /Users/<USER>/Downloads/Apps/Scripture\\ 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app>"]}, "P0:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Debug:ValidateDevelopmentAssets /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build": {"tool": "validate-development-assets", "description": "ValidateDevelopmentAssets /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build", "inputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Preview Content", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--entry>"], "outputs": ["<ValidateDevelopmentAssets-/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build>"], "allow-missing-inputs": true}, "P2:::WriteAuxiliaryFile /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60-VFS/all-product-headers.yaml": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60-VFS/all-product-headers.yaml", "inputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60-VFS/all-product-headers.yaml"]}, "P2:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Debug:ConstructStubExecutorLinkFileList /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture-ExecutorLinkFileList-normal-arm64.txt": {"tool": "construct-stub-executor-input-file-list", "description": "ConstructStubExecutorLinkFileList /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture-ExecutorLinkFileList-normal-arm64.txt", "inputs": ["/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib/libPreviewsJITStubExecutor_no_swift_entry_point.a", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib/libPreviewsJITStubExecutor.a", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/MacOS/Scripture.debug.dylib", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-linking>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture-ExecutorLinkFileList-normal-arm64.txt"]}, "P2:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Debug:Copy /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.swiftmodule/Project/arm64-apple-macos.swiftsourceinfo /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.swiftsourceinfo": {"tool": "file-copy", "description": "Copy /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.swiftmodule/Project/arm64-apple-macos.swiftsourceinfo /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.swiftsourceinfo", "inputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.swiftsourceinfo/", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--copy-headers-completion>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.swiftmodule/Project/arm64-apple-macos.swiftsourceinfo"]}, "P2:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Debug:Copy /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.swiftmodule/arm64-apple-macos.abi.json /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.abi.json": {"tool": "file-copy", "description": "Copy /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.swiftmodule/arm64-apple-macos.abi.json /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.abi.json", "inputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.abi.json/", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--copy-headers-completion>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.swiftmodule/arm64-apple-macos.abi.json"]}, "P2:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Debug:Copy /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.swiftmodule/arm64-apple-macos.swiftdoc /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.swiftdoc": {"tool": "file-copy", "description": "Copy /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.swiftmodule/arm64-apple-macos.swiftdoc /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.swiftdoc", "inputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.swiftdoc/", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--copy-headers-completion>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.swiftmodule/arm64-apple-macos.swiftdoc"]}, "P2:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Debug:Copy /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.swiftmodule/arm64-apple-macos.swiftmodule /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.swiftmodule": {"tool": "file-copy", "description": "Copy /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.swiftmodule/arm64-apple-macos.swiftmodule /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.swiftmodule", "inputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.swiftmodule/", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--copy-headers-completion>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.swiftmodule/arm64-apple-macos.swiftmodule"]}, "P2:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Debug:Ld /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/MacOS/Scripture normal": {"tool": "shell", "description": "Ld /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/MacOS/Scripture normal", "inputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/MacOS/Scripture.debug.dylib", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture-DebugDylibPath-normal-arm64.txt", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture-DebugDylibInstallName-normal-arm64.txt", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture-ExecutorLinkFileList-normal-arm64.txt", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-linking>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/MacOS/Scripture", "<Linked Binary /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/MacOS/Scripture>", "<TRIGGER: Ld /Users/<USER>/Downloads/Apps/Scripture\\ 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/MacOS/Scripture normal>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-<PERSON><PERSON><PERSON>", "-reproducible", "-target", "arm64-apple-macos14.0", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-O0", "-L/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug", "-F/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug", "-<PERSON><PERSON><PERSON>", "-rpath", "-<PERSON><PERSON><PERSON>", "@executable_path", "-<PERSON><PERSON><PERSON>", "-rpath", "-<PERSON><PERSON><PERSON>", "@executable_path/../Frameworks", "-rdynamic", "-<PERSON><PERSON><PERSON>", "-no_deduplicate", "-e", "___debug_blank_executor_main", "-<PERSON><PERSON><PERSON>", "-sectcreate", "-<PERSON><PERSON><PERSON>", "__TEXT", "-<PERSON><PERSON><PERSON>", "__debug_dylib", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture-DebugDylibPath-normal-arm64.txt", "-<PERSON><PERSON><PERSON>", "-sectcreate", "-<PERSON><PERSON><PERSON>", "__TEXT", "-<PERSON><PERSON><PERSON>", "__debug_instlnm", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture-DebugDylibInstallName-normal-arm64.txt", "-<PERSON><PERSON><PERSON>", "-filelist", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture-ExecutorLinkFileList-normal-arm64.txt", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/MacOS/Scripture.debug.dylib", "-<PERSON><PERSON><PERSON>", "-no_adhoc_codesign", "-o", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/MacOS/Scripture"], "env": {}, "working-directory": "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader", "signature": "300667b87bf6d04e12e1fa0ab8e8ffc1"}, "P2:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Debug:Ld /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/MacOS/Scripture.debug.dylib normal": {"tool": "shell", "description": "Ld /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/MacOS/Scripture.debug.dylib normal", "inputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/DownloadManager.o", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloaderApp.o", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.LinkFileList", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--generated-headers>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--swift-generated-headers>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-linking>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/MacOS/Scripture.debug.dylib", "<Linked Binary Debug Dylib /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/MacOS/Scripture.debug.dylib>", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture_lto.o", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture_dependency_info.dat", "<TRIGGER: Ld /Users/<USER>/Downloads/Apps/Scripture\\ 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/MacOS/Scripture.debug.dylib normal>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-<PERSON><PERSON><PERSON>", "-reproducible", "-target", "arm64-apple-macos14.0", "-dynamiclib", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-O0", "-L/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/EagerLinkingTBDs/Debug", "-L/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug", "-F/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/EagerLinkingTBDs/Debug", "-F/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug", "-filelist", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.LinkFileList", "-install_name", "@rpath/Scripture.debug.dylib", "-<PERSON><PERSON><PERSON>", "-rpath", "-<PERSON><PERSON><PERSON>", "@executable_path/../Frameworks", "-<PERSON><PERSON><PERSON>", "-object_path_lto", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture_lto.o", "-rdynamic", "-<PERSON><PERSON><PERSON>", "-no_deduplicate", "-<PERSON><PERSON><PERSON>", "-dependency_info", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture_dependency_info.dat", "-fobjc-link-runtime", "-L/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx", "-L/usr/lib/swift", "-<PERSON><PERSON><PERSON>", "-add_ast_path", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.swiftmodule", "-<PERSON><PERSON><PERSON>", "-alias", "-<PERSON><PERSON><PERSON>", "_main", "-<PERSON><PERSON><PERSON>", "___debug_main_executable_dylib_entry_point", "-<PERSON><PERSON><PERSON>", "-no_adhoc_codesign", "-o", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/MacOS/Scripture.debug.dylib"], "env": {}, "working-directory": "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader", "deps": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture_dependency_info.dat"], "deps-style": "dependency-info", "signature": "3185c8f8d9d5c4a480dfe7cc011886c9"}, "P2:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Debug:Ld /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/MacOS/__preview.dylib normal": {"tool": "shell", "description": "Ld /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/MacOS/__preview.dylib normal", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-linking>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/MacOS/__preview.dylib", "<Linked Binary Preview Injection Dylib /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/MacOS/__preview.dylib>", "<TRIGGER: Ld /Users/<USER>/Downloads/Apps/Scripture\\ 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/MacOS/__preview.dylib normal>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-<PERSON><PERSON><PERSON>", "-reproducible", "-target", "arm64-apple-macos14.0", "-dynamiclib", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-O0", "-L/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug", "-F/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug", "-install_name", "@rpath/Scripture.debug.dylib", "-rdynamic", "-<PERSON><PERSON><PERSON>", "-no_deduplicate", "-<PERSON><PERSON><PERSON>", "-dependency_info", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture_dependency_info.dat", "-<PERSON><PERSON><PERSON>", "-no_adhoc_codesign", "-o", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products/Debug/Scripture.app/Contents/MacOS/__preview.dylib"], "env": {}, "working-directory": "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader", "signature": "fb1e3f910bf58427d0d350c22371d9ef"}, "P2:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Debug:SwiftDriver Compilation Requirements YouTubeTranscriptDownloader normal arm64 com.apple.xcode.tools.swift.compiler": {"tool": "swift-driver-compilation-requirement", "description": "SwiftDriver Compilation Requirements YouTubeTranscriptDownloader normal arm64 com.apple.xcode.tools.swift.compiler", "inputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/ContentView.swift", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Managers/DownloadManager.swift", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/YouTubeTranscriptDownloaderApp.swift", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.SwiftFileList", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloader-OutputFileMap.json", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloader_const_extract_protocols.json", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture-generated-files.hmap", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture-own-target-headers.hmap", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture-all-target-headers.hmap", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture-project-headers.hmap", "<ClangStatCache /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--copy-headers-completion>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--ModuleVerifierTaskProducer>", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloader Swift Compilation Requirements Finished", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.swiftmodule", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.swiftsourceinfo", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.abi.json", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture-Swift.h", "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.swiftdoc"]}, "P2:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Debug:SwiftMergeGeneratedHeaders /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/DerivedSources/Scripture-Swift.h /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture-Swift.h": {"tool": "swift-header-tool", "description": "SwiftMergeGeneratedHeaders /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/DerivedSources/Scripture-Swift.h /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture-Swift.h", "inputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture-Swift.h", "<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/DerivedSources/Scripture-Swift.h"]}, "P2:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Debug:WriteAuxiliaryFile /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/DerivedSources/Entitlements.plist": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/DerivedSources/Entitlements.plist", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--immediate>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/DerivedSources/Entitlements.plist"]}, "P2:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Debug:WriteAuxiliaryFile /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.LinkFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.LinkFileList", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--immediate>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.LinkFileList"]}, "P2:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Debug:WriteAuxiliaryFile /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.SwiftConstValuesFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.SwiftConstValuesFileList", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--immediate>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.SwiftConstValuesFileList"]}, "P2:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Debug:WriteAuxiliaryFile /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.SwiftFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.SwiftFileList", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--immediate>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/Scripture.SwiftFileList"]}, "P2:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Debug:WriteAuxiliaryFile /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloader-OutputFileMap.json": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloader-OutputFileMap.json", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--immediate>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloader-OutputFileMap.json"]}, "P2:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Debug:WriteAuxiliaryFile /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloader_const_extract_protocols.json": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloader_const_extract_protocols.json", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--immediate>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloader_const_extract_protocols.json"]}, "P2:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Debug:WriteAuxiliaryFile /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture-DebugDylibInstallName-normal-arm64.txt": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture-DebugDylibInstallName-normal-arm64.txt", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--immediate>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture-DebugDylibInstallName-normal-arm64.txt"]}, "P2:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Debug:WriteAuxiliaryFile /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture-DebugDylibPath-normal-arm64.txt": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture-DebugDylibPath-normal-arm64.txt", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--immediate>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture-DebugDylibPath-normal-arm64.txt"]}, "P2:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Debug:WriteAuxiliaryFile /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture-all-non-framework-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture-all-non-framework-target-headers.hmap", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--immediate>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture-all-non-framework-target-headers.hmap"]}, "P2:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Debug:WriteAuxiliaryFile /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture-all-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture-all-target-headers.hmap", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--immediate>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture-all-target-headers.hmap"]}, "P2:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Debug:WriteAuxiliaryFile /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture-generated-files.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture-generated-files.hmap", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--immediate>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture-generated-files.hmap"]}, "P2:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Debug:WriteAuxiliaryFile /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture-own-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture-own-target-headers.hmap", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--immediate>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture-own-target-headers.hmap"]}, "P2:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Debug:WriteAuxiliaryFile /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture-project-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture-project-headers.hmap", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--immediate>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture-project-headers.hmap"]}, "P2:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Debug:WriteAuxiliaryFile /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture.DependencyMetadataFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture.DependencyMetadataFileList", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--immediate>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture.DependencyMetadataFileList"]}, "P2:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Debug:WriteAuxiliaryFile /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture.DependencyStaticMetadataFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture.DependencyStaticMetadataFileList", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--immediate>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture.DependencyStaticMetadataFileList"]}, "P2:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Debug:WriteAuxiliaryFile /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture.hmap", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--immediate>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Scripture.hmap"]}, "P2:target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788-:Debug:WriteAuxiliaryFile /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/empty-Scripture.plist": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/empty-Scripture.plist", "inputs": ["<target-YouTubeTranscriptDownloader-77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788--immediate>"], "outputs": ["/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/empty-Scripture.plist"]}}}