{"": {"diagnostics": "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloader-master.dia", "emit-module-dependencies": "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloader-master-emit-module.d", "emit-module-diagnostics": "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloader-master-emit-module.dia", "swift-dependencies": "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloader-master.swiftdeps"}, "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/ContentView.swift": {"const-values": "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/ContentView.swiftconstvalues", "dependencies": "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/ContentView.d", "diagnostics": "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/ContentView.dia", "index-unit-output-path": "/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/ContentView.o", "llvm-bc": "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/ContentView.bc", "object": "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/ContentView.o", "swift-dependencies": "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/ContentView.swiftdeps", "swiftmodule": "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/ContentView~partial.swiftmodule"}, "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Managers/DownloadManager.swift": {"const-values": "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/DownloadManager.swiftconstvalues", "dependencies": "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/DownloadManager.d", "diagnostics": "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/DownloadManager.dia", "index-unit-output-path": "/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/DownloadManager.o", "llvm-bc": "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/DownloadManager.bc", "object": "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/DownloadManager.o", "swift-dependencies": "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/DownloadManager.swiftdeps", "swiftmodule": "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/DownloadManager~partial.swiftmodule"}, "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/YouTubeTranscriptDownloaderApp.swift": {"const-values": "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloaderApp.swiftconstvalues", "dependencies": "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloaderApp.d", "diagnostics": "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloaderApp.dia", "index-unit-output-path": "/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloaderApp.o", "llvm-bc": "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloaderApp.bc", "object": "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloaderApp.o", "swift-dependencies": "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloaderApp.swiftdeps", "swiftmodule": "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/YouTubeTranscriptDownloaderApp~partial.swiftmodule"}, "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/DerivedSources/GeneratedAssetSymbols.swift": {"const-values": "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftconstvalues", "dependencies": "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/GeneratedAssetSymbols.d", "diagnostics": "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/GeneratedAssetSymbols.dia", "index-unit-output-path": "/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "llvm-bc": "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/GeneratedAssetSymbols.bc", "object": "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "swift-dependencies": "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftdeps", "swiftmodule": "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/YouTubeTranscriptDownloader.build/Debug/YouTubeTranscriptDownloader.build/Objects-normal/arm64/GeneratedAssetSymbols~partial.swiftmodule"}}