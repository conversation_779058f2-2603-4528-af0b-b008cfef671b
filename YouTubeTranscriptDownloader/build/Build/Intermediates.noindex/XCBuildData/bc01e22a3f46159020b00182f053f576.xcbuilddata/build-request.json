{"buildCommand": {"command": "build", "skipDependencies": false, "style": "buildOnly"}, "configuredTargets": [{"guid": "77dc07c15c29080e076455d5e8c2ee60f31307582bc8e072ac998e289c603788"}], "containerPath": "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader.xcodeproj", "continueBuildingAfterErrors": false, "dependencyScope": "workspace", "enableIndexBuildArena": false, "hideShellScriptEnvironment": false, "parameters": {"action": "build", "activeArchitecture": "arm64", "activeRunDestination": {"disableOnlyActiveArch": false, "platform": "macosx", "sdk": "macosx15.5", "sdkVariant": "macos", "supportedArchitectures": ["arm64e", "arm64", "x86_64"], "targetArchitecture": "arm64"}, "arenaInfo": {"buildIntermediatesPath": "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex", "buildProductsPath": "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Products", "derivedDataPath": "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build", "indexDataStoreFolderPath": "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Index.noindex/DataStore", "indexEnableDataStore": true, "indexPCHPath": "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Index.noindex/PrecompiledHeaders", "pchPath": "/Users/<USER>/Downloads/Apps/Scripture 3/YouTubeTranscriptDownloader/build/Build/Intermediates.noindex/PrecompiledHeaders"}, "configurationName": "Debug", "overrides": {"commandLine": {"table": {}}, "synthesized": {"table": {"ACTION": "build", "COLOR_DIAGNOSTICS": "YES", "diagnostic_message_length": "80", "ENABLE_PREVIEWS": "NO", "ENABLE_XOJIT_PREVIEWS": "YES", "ONLY_ACTIVE_ARCH": "YES"}}}}, "schemeCommand": "launch", "showNonLoggedProgress": true, "useDryRun": false, "useImplicitDependencies": true, "useLegacyBuildLocations": false, "useParallelTargets": true}