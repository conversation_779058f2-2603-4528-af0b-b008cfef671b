#!/usr/bin/env python3
"""
Script to get YouTube playlist videos using pytubefix
Usage: python3 get_playlist_videos.py <playlist_url> <max_videos>
"""

import sys
import json
import re
from datetime import datetime

try:
    from pytubefix import Playlist
    PYTUBEFIX_AVAILABLE = True
except ImportError:
    PYTUBEFIX_AVAILABLE = False

def is_playlist_url(url):
    """Check if URL is a YouTube playlist URL"""
    playlist_patterns = [
        r'youtube\.com/playlist\?list=',
        r'youtube\.com/watch\?.*list=',
        r'youtu\.be/.*\?.*list='
    ]
    return any(re.search(pattern, url) for pattern in playlist_patterns)

def get_playlist_videos(playlist_url, max_videos=50):
    """Get video URLs from a YouTube playlist"""
    try:
        if not PYTUBEFIX_AVAILABLE:
            return {
                "error": "Failed to install or import pytubefix library. Please install manually: pip3 install pytubefix",
                "success": False,
                "videos": []
            }
        
        # Validate playlist URL format
        if not playlist_url or not is_playlist_url(playlist_url):
            return {
                "error": f"Invalid YouTube playlist URL format: {playlist_url}",
                "success": False,
                "videos": []
            }

        print(f"Getting videos from playlist: {playlist_url}", file=sys.stderr)
        
        # Create playlist object
        playlist = Playlist(playlist_url)
        
        # Get playlist info
        try:
            playlist_title = playlist.title
            print(f"Playlist title: {playlist_title}", file=sys.stderr)
        except Exception as e:
            playlist_title = "Unknown Playlist"
            print(f"Could not get playlist title: {e}", file=sys.stderr)
        
        # Get videos (limited to max_videos)
        videos = []
        video_count = 0

        try:
            for video in playlist.videos:
                if video_count >= max_videos:
                    break
                
                try:
                    # Get video info
                    video_title = video.title
                    video_url = video.watch_url
                    video_id = video.video_id
                    
                    # Get video metadata
                    try:
                        author = video.author
                        duration = video.length
                        view_count = video.views
                        publish_date = video.publish_date.isoformat() if video.publish_date else None
                    except Exception as e:
                        print(f"Warning: Could not get metadata for video {video_id}: {e}", file=sys.stderr)
                        author = "Unknown"
                        duration = 0
                        view_count = 0
                        publish_date = None
                    
                    video_info = {
                        "title": video_title,
                        "url": video_url,
                        "video_id": video_id,
                        "author": author,
                        "duration": duration,
                        "view_count": view_count,
                        "publish_date": publish_date
                    }
                    
                    videos.append(video_info)
                    video_count += 1
                    
                    print(f"Added video {video_count}: {video_title}", file=sys.stderr)
                    
                except Exception as e:
                    print(f"Error processing video: {e}", file=sys.stderr)
                    continue
                    
        except Exception as e:
            print(f"Error iterating through playlist videos: {e}", file=sys.stderr)
            if not videos:  # If no videos were processed at all
                return {
                    "error": f"Failed to get videos from playlist: {str(e)}",
                    "success": False,
                    "videos": []
                }
        
        print(f"Successfully retrieved {len(videos)} videos from playlist", file=sys.stderr)
        
        return {
            "success": True,
            "playlist_title": playlist_title,
            "playlist_url": playlist_url,
            "video_count": len(videos),
            "videos": videos,
            "retrieved_at": datetime.now().isoformat()
        }
        
    except Exception as e:
        error_msg = f"Error getting playlist videos: {str(e)}"
        print(error_msg, file=sys.stderr)
        return {
            "error": error_msg,
            "success": False,
            "videos": []
        }

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print(json.dumps({"error": "Playlist URL required", "success": False, "videos": []}))
        sys.exit(1)

    playlist_url = sys.argv[1]
    max_videos = int(sys.argv[2]) if len(sys.argv) > 2 else 50

    result = get_playlist_videos(playlist_url, max_videos)
    print(json.dumps(result, indent=2, ensure_ascii=False))
    
    if result["success"]:
        sys.exit(0)
    else:
        sys.exit(1)
