import SwiftUI

struct ContentView: View {
    @StateObject private var downloadManager = DownloadManager()
    @EnvironmentObject private var errorManager: ErrorManager
    @State private var quickVideoURL = ""
    @State private var newChannelURL = ""
    @State private var newChannelLiveLimit = 10
    @State private var newChannelRegularLimit = 10
    @State private var newChannelLanguage: PreferredLanguage = .auto
    @State private var isDownloading = false
    @State private var statusMessage = "Ready to download"
    @State private var currentlyProcessingChannel: String? = nil
    @State private var selectedChannels: Set<UUID> = []
    @State private var searchText = ""
    @State private var sortOption: ChannelSortOption = .name
    @State private var showQuickDownloadSheet = false
    @State private var showAddChannelSheet = false
    @State private var showEditChannelSheet = false
    @State private var editingChannel: SavedChannel?
    @State private var editChannelName = ""
    @State private var editChannelURL = ""
    @State private var editLiveLimit = 10
    @State private var editRegularLimit = 10
    @State private var editPreferredLanguage: PreferredLanguage = .auto
    @State private var editDownloadLiveVideos = true
    @State private var editDownloadRegularVideos = true

    enum ChannelSortOption: String, CaseIterable {
        case name = "Name"
        case dateAdded = "Date Added"
        case videoCount = "Video Count"
    }

    var body: some View {
        VStack(spacing: 0) {
            // Main Content - Two Panel Layout
            GeometryReader { geometry in
                HStack(spacing: 0) {
                    // Left Panel - Actions & Input (responsive width)
                    leftPanel
                        .frame(minWidth: 300, maxWidth: geometry.size.width * 0.4, maxHeight: .infinity)
                        .background(Color(NSColor.controlBackgroundColor))

                    // Divider
                    Divider()

                    // Right Panel - Library & Queue (responsive width)
                    rightPanel
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                        .background(Color(NSColor.windowBackgroundColor))
                }
            }
        }
        .frame(minWidth: 800, minHeight: 600)
        .onAppear {
            statusMessage = "Ready to download"
            // Reset downloading state on app start
            isDownloading = false
            // Clear any failed downloads from previous sessions
            downloadManager.clearCompleted()
            downloadManager.downloadQueue.removeAll { $0.status == .failed }
            print("🔥 App appeared, isDownloading reset to: \(isDownloading)")
            print("🔥 Download queue cleared")

            // Check and install pytubefix on first startup
            checkAndInstallPytubefix()
        }
        .alert("Error", isPresented: $errorManager.showErrorAlert) {
            if let error = errorManager.currentError {
                if error.isRecoverable {
                    Button("OK") {
                        errorManager.clearError()
                    }
                    if let _ = error.recoveryAction {
                        Button("Retry") {
                            errorManager.clearError()
                            // Add retry logic here if needed
                        }
                    }
                } else {
                    Button("OK") {
                        errorManager.clearError()
                    }
                }
            }
        } message: {
            if let error = errorManager.currentError {
                VStack(alignment: .leading, spacing: 8) {
                    Text(error.message)

                    if let recovery = error.recoveryAction {
                        Text("Suggested action: \(recovery)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }
        }
    }
    

    
    // MARK: - Left Panel
    private var leftPanel: some View {
        VStack(alignment: .leading, spacing: 0) {
            // Fixed Action Buttons at Top
            VStack(spacing: 12) {
                Button(action: { showQuickDownloadSheet = true }) {
                    HStack {
                        Image(systemName: "bolt.fill")
                            .foregroundColor(.orange)
                        Text("Quick Download")
                            .fontWeight(.medium)
                    }
                    .frame(maxWidth: .infinity)
                }
                .buttonStyle(.borderedProminent)
                .controlSize(.large)

                Button(action: { showAddChannelSheet = true }) {
                    HStack {
                        Image(systemName: "folder.badge.plus")
                            .foregroundColor(.blue)
                        Text("Add Channel")
                            .fontWeight(.medium)
                    }
                    .frame(maxWidth: .infinity)
                }
                .buttonStyle(.bordered)
                .controlSize(.large)
            }
            .padding(.bottom, 20)

                Button(action: {
                    statusMessage = "Preparing to download all channels..."
                    downloadAllChannels()
                }) {
                    HStack {
                        Image(systemName: "arrow.down.circle.fill")
                            .foregroundColor(.green)
                        Text("Download All")
                            .fontWeight(.medium)
                    }
                    .frame(maxWidth: .infinity)
                }
                .buttonStyle(.bordered)
                .controlSize(.large)
                .disabled(isDownloading || downloadManager.savedChannels.isEmpty)

            Divider()
                .padding(.bottom, 20)

            // Download Queue Section (flexible)
            downloadQueueSection
        }
        .padding(20)
        .sheet(isPresented: $showQuickDownloadSheet) {
            quickDownloadSheet
        }
        .sheet(isPresented: $showAddChannelSheet) {
            addChannelSheet
        }
        .sheet(isPresented: $showEditChannelSheet) {
            editChannelSheet
        }
    }
    

    

    
    // MARK: - Right Panel
    private var rightPanel: some View {
        // Sources Library (Full Height)
        sourcesLibrarySection
    }

    // MARK: - Sources Library Section
    private var sourcesLibrarySection: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Header with search and controls
            VStack(spacing: 12) {
                HStack {
                    Image(systemName: "tv.fill")
                        .foregroundColor(.blue)
                    Text("Sources Library")
                        .font(.headline)
                        .fontWeight(.semibold)
                    Spacer()
                    Text("\(downloadManager.savedChannels.count) sources")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                HStack {
                    TextField("Search channels...", text: $searchText)
                        .textFieldStyle(.roundedBorder)

                    // Sort picker
                    Picker("Sort", selection: $sortOption) {
                        ForEach(ChannelSortOption.allCases, id: \.self) { option in
                            Text(option.rawValue).tag(option)
                        }
                    }
                    .pickerStyle(.menu)
                    .frame(width: 120)

                    // Timestamp toggle
                    Toggle("Timestamps", isOn: $downloadManager.includeTimestamps)
                        .font(.caption)
                        .controlSize(.small)

                    // Folders toggle
                    Toggle("Folders", isOn: $downloadManager.useFolders)
                        .font(.caption)
                        .controlSize(.small)
                }

                // Output folder controls
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Output Folder:")
                            .font(.caption)
                            .fontWeight(.medium)
                        Text(downloadManager.outputDirectory.lastPathComponent)
                            .font(.caption2)
                            .foregroundColor(.secondary)
                            .lineLimit(1)
                    }

                    Spacer()

                    HStack(spacing: 8) {
                        Button("Change") {
                            selectOutputFolder()
                        }
                        .buttonStyle(.bordered)
                        .controlSize(.small)

                        Button("Show") {
                            downloadManager.showOutputFolder()
                        }
                        .buttonStyle(.borderedProminent)
                        .controlSize(.small)

                        Button("Export") {
                            exportChannels()
                        }
                        .buttonStyle(.bordered)
                        .controlSize(.small)
                        .disabled(downloadManager.savedChannels.isEmpty)

                        Button("Import") {
                            importChannels()
                        }
                        .buttonStyle(.bordered)
                        .controlSize(.small)
                    }
                }
            }

            // Batch operations
            if !downloadManager.savedChannels.isEmpty {
                HStack(spacing: 8) {
                    Button(action: selectAllChannels) {
                        HStack(spacing: 4) {
                            Image(systemName: selectedChannels.count == downloadManager.savedChannels.count ? "checkmark.square.fill" : "square")
                                .font(.caption)
                            Text(selectedChannels.count == downloadManager.savedChannels.count ? "Deselect All" : "Select All")
                                .font(.caption)
                        }
                    }
                    .buttonStyle(.bordered)
                    .controlSize(.small)

                    if !selectedChannels.isEmpty {
                        Button(action: downloadSelectedChannels) {
                            HStack(spacing: 4) {
                                Image(systemName: "arrow.down.circle")
                                    .font(.caption)
                                Text("Download \(selectedChannels.count)")
                                    .font(.caption)
                            }
                        }
                        .buttonStyle(.borderedProminent)
                        .controlSize(.small)
                        .disabled(isDownloading)
                    }

                    Spacer()
                }
            }

            // Channel List
            if downloadManager.savedChannels.isEmpty {
                VStack(spacing: 12) {
                    Image(systemName: "tv.slash")
                        .font(.largeTitle)
                        .foregroundColor(.secondary)
                    Text("No channels added yet")
                        .font(.headline)
                        .foregroundColor(.secondary)
                    Text("Add a channel using the form on the left")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
            } else {
                ScrollView {
                    LazyVStack(spacing: 1) {
                        ForEach(filteredChannels, id: \.id) { channel in
                            channelListRow(channel)
                        }
                    }
                }
            }
        }
        .padding(20)
    }

    // MARK: - Channel List Row
    private func channelListRow(_ channel: SavedChannel) -> some View {
        HStack(spacing: 12) {
            // Selection checkbox
            Button(action: { toggleChannelSelection(channel.id) }) {
                Image(systemName: selectedChannels.contains(channel.id) ? "checkmark.square.fill" : "square")
                    .foregroundColor(selectedChannels.contains(channel.id) ? .blue : .secondary)
                    .font(.system(size: 14))
            }
            .buttonStyle(.plain)

            // Channel thumbnail or icon
            if let thumbnailURL = channel.thumbnailURL, let url = URL(string: thumbnailURL) {
                AsyncImage(url: url) { image in
                    image
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                } placeholder: {
                    Image(systemName: "tv.fill")
                        .foregroundColor(.blue)
                        .font(.system(size: 14))
                }
                .frame(width: 32, height: 32)
                .clipShape(Circle())
            } else {
                Image(systemName: "tv.fill")
                    .foregroundColor(.blue)
                    .font(.system(size: 14))
                    .frame(width: 32, height: 32)
            }

            // Channel info
            VStack(alignment: .leading, spacing: 2) {
                HStack(spacing: 4) {
                    // Source type indicator
                    Image(systemName: channel.sourceType == .playlist ? "list.bullet" : "tv")
                        .foregroundColor(channel.sourceType == .playlist ? .purple : .blue)
                        .font(.system(size: 10))

                    Text(channel.name)
                        .font(.system(size: 13, weight: .medium))
                        .lineLimit(1)

                    if currentlyProcessingChannel == channel.name {
                        HStack(spacing: 2) {
                            Image(systemName: "arrow.clockwise")
                                .foregroundColor(.orange)
                                .font(.system(size: 10))
                                .rotationEffect(.degrees(isDownloading ? 360 : 0))
                                .animation(.linear(duration: 1).repeatForever(autoreverses: false), value: isDownloading)
                            Text("Processing...")
                                .font(.system(size: 10, weight: .medium))
                                .foregroundColor(.orange)
                        }
                    }
                }

                HStack(spacing: 8) {
                    HStack(spacing: 4) {
                        Image(systemName: "dot.radiowaves.left.and.right")
                            .foregroundColor(channel.downloadLiveVideos ? .red : .gray)
                            .font(.system(size: 9))
                        Text("\(channel.liveVideoLimit)")
                            .font(.system(size: 11))
                            .foregroundColor(channel.downloadLiveVideos ? .secondary : .gray)
                            .strikethrough(!channel.downloadLiveVideos)
                    }

                    HStack(spacing: 4) {
                        Image(systemName: "play.rectangle")
                            .foregroundColor(channel.downloadRegularVideos ? .blue : .gray)
                            .font(.system(size: 9))
                        Text("\(channel.regularVideoLimit)")
                            .font(.system(size: 11))
                            .foregroundColor(channel.downloadRegularVideos ? .secondary : .gray)
                            .strikethrough(!channel.downloadRegularVideos)
                    }

                    if let lastDownloaded = channel.lastDownloaded {
                        Text("Downloaded \(formatDate(lastDownloaded))")
                            .font(.system(size: 11))
                            .foregroundColor(.green)
                    } else {
                        Text("Never downloaded")
                            .font(.system(size: 11))
                            .foregroundColor(.secondary)
                    }
                }
            }

            Spacer()

            // Action buttons
            HStack(spacing: 6) {
                Button(action: {
                    print("🔥 Download button clicked! isDownloading: \(isDownloading)")
                    // Instant feedback
                    statusMessage = "Starting download for \(channel.name)..."
                    smartDownloadChannel(channel)
                }) {
                    Image(systemName: "arrow.down.circle.fill")
                        .foregroundColor(.blue)
                        .font(.system(size: 18))
                }
                .buttonStyle(.plain)
                .disabled(isDownloading)
                .help("Update and download channel videos")

                Button(action: {
                    editingChannel = channel
                    editChannelName = channel.name
                    editChannelURL = channel.url
                    editLiveLimit = channel.liveVideoLimit
                    editRegularLimit = channel.regularVideoLimit
                    editPreferredLanguage = channel.preferredLanguage
                    editDownloadLiveVideos = channel.downloadLiveVideos
                    editDownloadRegularVideos = channel.downloadRegularVideos
                    showEditChannelSheet = true
                }) {
                    Image(systemName: "pencil.circle")
                        .foregroundColor(.orange)
                        .font(.system(size: 18))
                }
                .buttonStyle(.plain)
                .disabled(isDownloading)
                .help("Edit channel settings")

                Button(action: { showChannelFolder(channel) }) {
                    Image(systemName: "folder.circle")
                        .foregroundColor(.green)
                        .font(.system(size: 18))
                }
                .buttonStyle(.plain)
                .help("Show channel folder")

                Button(action: { removeChannel(channel) }) {
                    Image(systemName: "trash.circle")
                        .foregroundColor(.red)
                        .font(.system(size: 18))
                }
                .buttonStyle(.plain)
                .help("Remove channel")
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 8)
        .background(Color(NSColor.controlBackgroundColor))
        .overlay(
            Rectangle()
                .frame(height: 0.5)
                .foregroundColor(Color(NSColor.separatorColor)),
            alignment: .bottom
        )
        .overlay(
            RoundedRectangle(cornerRadius: 8)
                .stroke(selectedChannels.contains(channel.id) ? Color.blue : Color.clear, lineWidth: 2)
        )
    }

    // MARK: - Download Queue Section
    private var downloadQueueSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            // Fixed header
            HStack {
                Image(systemName: "list.bullet.rectangle")
                    .foregroundColor(.green)
                Text("Download Queue")
                    .font(.headline)
                    .fontWeight(.semibold)
                Spacer()

                if isDownloading || downloadManager.queueCount > 0 {
                    Button(action: {
                        stopAllDownloads()
                    }) {
                        Image(systemName: "stop.circle.fill")
                            .foregroundColor(.red)
                            .font(.system(size: 14))
                    }
                    .buttonStyle(.plain)
                    .help("Stop all downloads")
                }

                Text("\(downloadManager.downloadQueue.count)")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            // Fixed status indicator
            HStack(spacing: 4) {
                if isDownloading {
                    ProgressView()
                        .scaleEffect(0.6)
                } else {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                        .font(.caption)
                }
                Text(statusMessage)
                    .font(.caption2)
                    .lineLimit(1)
            }
            .frame(height: 20) // Fixed height for status

            // Flexible queue content
            Group {
                if downloadManager.downloadQueue.isEmpty {
                    Text("No active downloads")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .frame(maxWidth: .infinity, alignment: .center)
                        .frame(minHeight: 100) // Minimum height when empty
                } else {
                    ScrollView {
                        LazyVStack(spacing: 4) {
                            ForEach(downloadManager.downloadQueue) { item in
                                compactDownloadItemRow(item)
                            }
                        }
                        .padding(.top, 4)
                    }
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                }
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding(.horizontal, 20)
    }

    // MARK: - Download Item Row
    private func downloadItemRow(_ item: DownloadItem) -> some View {
        HStack(spacing: 12) {
            // Status icon
            statusIcon(for: item.status)

            // Content
            VStack(alignment: .leading, spacing: 4) {
                Text(item.title)
                    .font(.subheadline)
                    .lineLimit(1)

                HStack {
                    Text(item.channelName)
                        .font(.caption2)
                        .foregroundColor(.secondary)

                    Spacer()

                    if item.status == .downloading {
                        ProgressView(value: item.progress)
                            .frame(width: 60)
                    }
                }

                if item.status == .failed, let error = item.error {
                    Text("Error: \(error)")
                        .font(.caption2)
                        .foregroundColor(.red)
                        .lineLimit(2)
                }
            }

            // Actions
            VStack(spacing: 4) {
                if item.status == .failed {
                    Button("Retry") {
                        retryDownload(item)
                    }
                    .buttonStyle(.bordered)
                    .controlSize(.mini)
                }

                if item.status == .completed {
                    Button("Show") {
                        showFile(item)
                    }
                    .buttonStyle(.bordered)
                    .controlSize(.mini)
                }
            }
        }
        .padding(8)
        .background(backgroundColorForStatus(item.status))
        .cornerRadius(6)
    }

    // MARK: - Compact Download Item Row
    private func compactDownloadItemRow(_ item: DownloadItem) -> some View {
        HStack(spacing: 8) {
            // Status icon (smaller)
            statusIcon(for: item.status)
                .font(.caption)

            // Content (compact)
            VStack(alignment: .leading, spacing: 2) {
                Text(item.title)
                    .font(.caption)
                    .lineLimit(1)

                if item.status == .downloading {
                    ProgressView(value: item.progress)
                        .frame(height: 2)
                } else if item.status == .failed {
                    Text("Failed")
                        .font(.caption2)
                        .foregroundColor(.red)
                }
            }

            Spacer()
        }
        .padding(.horizontal, 8)
        .padding(.vertical, 4)
        .background(backgroundColorForStatus(item.status).opacity(0.3))
        .cornerRadius(4)
    }

    // MARK: - Sheet Views
    private var quickDownloadSheet: some View {
        VStack(alignment: .leading, spacing: 20) {
            HStack {
                Image(systemName: "bolt.fill")
                    .foregroundColor(.orange)
                    .font(.title2)
                Text("Quick Download")
                    .font(.title2)
                    .fontWeight(.bold)
                Spacer()
                Button("Cancel") {
                    showQuickDownloadSheet = false
                }
            }

            Text("Download a single video transcript instantly")
                .font(.subheadline)
                .foregroundColor(.secondary)

            TextField("Paste YouTube video URL here", text: $quickVideoURL)
                .textFieldStyle(.roundedBorder)
                .font(.body)

            HStack(spacing: 12) {
                Button("Cancel") {
                    showQuickDownloadSheet = false
                }
                .buttonStyle(.bordered)

                Spacer()

                Button("Download Now") {
                    downloadSingleVideo()
                    showQuickDownloadSheet = false
                }
                .buttonStyle(.borderedProminent)
                .disabled(quickVideoURL.isEmpty)
            }
        }
        .padding(24)
        .frame(width: 400)
    }

    private var addChannelSheet: some View {
        VStack(alignment: .leading, spacing: 20) {
            HStack {
                Image(systemName: "folder.badge.plus")
                    .foregroundColor(.blue)
                    .font(.title2)
                Text("Add New Source")
                    .font(.title2)
                    .fontWeight(.bold)
                Spacer()
                Button("Cancel") {
                    showAddChannelSheet = false
                }
            }

            Text("Add channels or playlists to your library for easy re-downloading")
                .font(.subheadline)
                .foregroundColor(.secondary)

            TextField("Paste YouTube channel or playlist URL here", text: $newChannelURL)
                .textFieldStyle(.roundedBorder)
                .font(.body)

            VStack(alignment: .leading, spacing: 16) {
                // Live Videos Limit
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Image(systemName: "dot.radiowaves.left.and.right")
                            .foregroundColor(.red)
                            .font(.caption)
                        Text("Live Videos Limit:")
                            .font(.subheadline)
                            .fontWeight(.medium)
                        Spacer()
                        TextField("10", value: $newChannelLiveLimit, format: .number)
                            .textFieldStyle(.roundedBorder)
                            .frame(width: 60)
                            .multilineTextAlignment(.center)
                    }

                    HStack {
                        Text("1")
                            .font(.caption2)
                            .foregroundColor(.secondary)

                        Slider(value: Binding(
                            get: { Double(newChannelLiveLimit) },
                            set: { newChannelLiveLimit = Int($0) }
                        ), in: 1...100, step: 1)

                        Text("100")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                }

                // Regular Videos Limit
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Image(systemName: "play.rectangle")
                            .foregroundColor(.blue)
                            .font(.caption)
                        Text("Regular Videos Limit:")
                            .font(.subheadline)
                            .fontWeight(.medium)
                        Spacer()
                        TextField("10", value: $newChannelRegularLimit, format: .number)
                            .textFieldStyle(.roundedBorder)
                            .frame(width: 60)
                            .multilineTextAlignment(.center)
                    }

                    HStack {
                        Text("1")
                            .font(.caption2)
                            .foregroundColor(.secondary)

                        Slider(value: Binding(
                            get: { Double(newChannelRegularLimit) },
                            set: { newChannelRegularLimit = Int($0) }
                        ), in: 1...100, step: 1)

                        Text("100")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                }

                // Language Selection
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Image(systemName: "globe")
                            .foregroundColor(.purple)
                            .font(.caption)
                        Text("Preferred Language:")
                            .font(.subheadline)
                            .fontWeight(.medium)
                    }

                    Picker("Language", selection: $newChannelLanguage) {
                        ForEach(PreferredLanguage.allCases, id: \.self) { language in
                            Text(language.displayName).tag(language)
                        }
                    }
                    .pickerStyle(.menu)
                    .frame(maxWidth: .infinity, alignment: .leading)
                }
            }

            HStack(spacing: 12) {
                Button("Cancel") {
                    showAddChannelSheet = false
                }
                .buttonStyle(.bordered)

                Spacer()

                Button("Add to Library") {
                    print("🔥🔥🔥 Add to Library button clicked!")
                    print("🔥🔥🔥 newChannelURL: '\(newChannelURL)'")
                    addSourceToLibrary()
                    showAddChannelSheet = false
                    print("🔥🔥🔥 Button action finished")
                }
                .buttonStyle(.borderedProminent)
                .disabled(newChannelURL.isEmpty)
            }
        }
        .padding(24)
        .frame(width: 450)
    }

    private var editChannelSheet: some View {
        VStack(alignment: .leading, spacing: 20) {
            HStack {
                Image(systemName: "pencil.circle")
                    .foregroundColor(.orange)
                    .font(.title2)
                Text("Edit Source")
                    .font(.title2)
                    .fontWeight(.bold)
                Spacer()
                Button("Cancel") {
                    showEditChannelSheet = false
                    editingChannel = nil
                }
            }

            if let channel = editingChannel {
                Text("Modify \(channel.sourceType.displayName.lowercased()) settings and information")
                    .font(.subheadline)
                    .foregroundColor(.secondary)

                VStack(alignment: .leading, spacing: 16) {
                    VStack(alignment: .leading, spacing: 8) {
                        Text("\(channel.sourceType.displayName) Name:")
                            .font(.subheadline)
                            .fontWeight(.medium)
                        TextField("\(channel.sourceType.displayName) name", text: $editChannelName)
                            .textFieldStyle(.roundedBorder)
                    }

                    VStack(alignment: .leading, spacing: 8) {
                        Text("\(channel.sourceType.displayName) URL:")
                            .font(.subheadline)
                            .fontWeight(.medium)
                        TextField("\(channel.sourceType.displayName) URL", text: $editChannelURL)
                            .textFieldStyle(.roundedBorder)
                    }

                    VStack(alignment: .leading, spacing: 16) {
                        // Live Videos Limit
                        VStack(alignment: .leading, spacing: 8) {
                            HStack {
                                Toggle("", isOn: $editDownloadLiveVideos)
                                    .controlSize(.small)
                                Image(systemName: "dot.radiowaves.left.and.right")
                                    .foregroundColor(.red)
                                    .font(.caption)
                                Text("Live Videos Limit:")
                                    .font(.subheadline)
                                    .fontWeight(.medium)
                            }

                            HStack {
                                TextField("10", value: $editLiveLimit, format: .number)
                                    .textFieldStyle(.roundedBorder)
                                    .frame(width: 60)
                                    .multilineTextAlignment(.center)
                                    .disabled(!editDownloadLiveVideos)

                                Text(editDownloadLiveVideos ? "live videos" : "live videos (disabled)")
                                    .font(.caption)
                                    .foregroundColor(editDownloadLiveVideos ? .secondary : .gray)
                            }
                        }

                        // Regular Videos Limit
                        VStack(alignment: .leading, spacing: 8) {
                            HStack {
                                Toggle("", isOn: $editDownloadRegularVideos)
                                    .controlSize(.small)
                                Image(systemName: "play.rectangle")
                                    .foregroundColor(.blue)
                                    .font(.caption)
                                Text("Regular Videos Limit:")
                                    .font(.subheadline)
                                    .fontWeight(.medium)
                            }

                            HStack {
                                TextField("10", value: $editRegularLimit, format: .number)
                                    .textFieldStyle(.roundedBorder)
                                    .frame(width: 60)
                                    .multilineTextAlignment(.center)
                                    .disabled(!editDownloadRegularVideos)

                                Text(editDownloadRegularVideos ? "regular videos" : "regular videos (disabled)")
                                    .font(.caption)
                                    .foregroundColor(editDownloadRegularVideos ? .secondary : .gray)
                            }
                        }

                        // Preferred Language
                        VStack(alignment: .leading, spacing: 8) {
                            HStack {
                                Image(systemName: "globe")
                                    .foregroundColor(.green)
                                    .font(.caption)
                                Text("Preferred Language:")
                                    .font(.subheadline)
                                    .fontWeight(.medium)
                            }

                            Picker("Language", selection: $editPreferredLanguage) {
                                ForEach(PreferredLanguage.allCases, id: \.self) { language in
                                    Text(language.displayName)
                                        .tag(language)
                                }
                            }
                            .pickerStyle(.menu)
                            .frame(maxWidth: .infinity, alignment: .leading)

                            Text("Language preference for transcript downloads")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }

                        // Warning if both are disabled
                        if !editDownloadLiveVideos && !editDownloadRegularVideos {
                            HStack {
                                Image(systemName: "exclamationmark.triangle")
                                    .foregroundColor(.orange)
                                    .font(.caption)
                                Text("At least one video type must be enabled")
                                    .font(.caption)
                                    .foregroundColor(.orange)
                            }
                        }
                    }

                    VStack(alignment: .leading, spacing: 8) {
                        Text("Added:")
                            .font(.subheadline)
                            .fontWeight(.medium)
                        Text(formatDate(channel.dateAdded))
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }

                HStack(spacing: 12) {
                    Button("Cancel") {
                        showEditChannelSheet = false
                        editingChannel = nil
                    }
                    .buttonStyle(.bordered)

                    Spacer()

                    Button("Save Changes") {
                        saveChannelChanges()
                    }
                    .buttonStyle(.borderedProminent)
                    .disabled(editChannelName.isEmpty || editChannelURL.isEmpty || editLiveLimit < 1 || editRegularLimit < 1 || (!editDownloadLiveVideos && !editDownloadRegularVideos))
                }
            }
        }
        .padding(24)
        .frame(width: 500)
    }

    // MARK: - System Controls Section
    private var systemControlsSection: some View {
        VStack(spacing: 12) {
            Divider()

            VStack(alignment: .leading, spacing: 8) {
                Text("Output Folder:")
                    .font(.subheadline)
                    .fontWeight(.medium)

                Text(downloadManager.outputDirectory.path)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(2)

                HStack(spacing: 8) {
                    Button("Change Folder") {
                        selectOutputFolder()
                    }
                    .buttonStyle(.bordered)
                    .controlSize(.small)

                    Button("Show Folder") {
                        downloadManager.showOutputFolder()
                    }
                    .buttonStyle(.borderedProminent)
                    .controlSize(.small)
                }
            }
        }
    }

    // MARK: - Computed Properties
    private var filteredChannels: [SavedChannel] {
        let filtered = searchText.isEmpty ? downloadManager.savedChannels : downloadManager.savedChannels.filter {
            $0.name.localizedCaseInsensitiveContains(searchText)
        }

        return filtered.sorted { channel1, channel2 in
            switch sortOption {
            case .name:
                return channel1.name.localizedCaseInsensitiveCompare(channel2.name) == .orderedAscending
            case .dateAdded:
                return channel1.dateAdded > channel2.dateAdded
            case .videoCount:
                return channel1.videoCount > channel2.videoCount
            }
        }
    }

    // MARK: - Helper Methods

    private func findPythonScript(_ scriptName: String) -> String? {
        let bundlePath = Bundle.main.bundlePath
        print("Bundle path: \(bundlePath)")

        // Create debug file
        let debugFile = "/tmp/script_search_debug.txt"
        var debugInfo = "Searching for script: \(scriptName)\n"
        debugInfo += "Bundle path: \(bundlePath)\n"

        let possiblePaths = [
            // Bundled resource
            Bundle.main.path(forResource: scriptName, ofType: "py"),
            // Development location - direct path
            "/Users/<USER>/Downloads/Apps/Scripture 2/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Python/\(scriptName).py",
            // Development location - relative to bundle
            bundlePath.replacingOccurrences(of: "/Build/Products/Debug/YouTubeTranscriptDownloader.app", with: "/YouTubeTranscriptDownloader/Python/\(scriptName).py"),
            // Alternative development location
            bundlePath.replacingOccurrences(of: "/Library/Developer/Xcode/DerivedData", with: "/Downloads/Apps/Scripture 2") + "/YouTubeTranscriptDownloader/Python/\(scriptName).py"
        ]

        for path in possiblePaths {
            if let path = path, FileManager.default.fileExists(atPath: path) {
                print("Found \(scriptName).py at: \(path)")
                debugInfo += "FOUND at: \(path)\n"
                try? debugInfo.write(toFile: debugFile, atomically: true, encoding: .utf8)
                return path
            } else {
                print("Not found at: \(path ?? "nil")")
                debugInfo += "Not found at: \(path ?? "nil")\n"
            }
        }

        print("Could not find \(scriptName).py script in any location")
        debugInfo += "SCRIPT NOT FOUND ANYWHERE\n"
        try? debugInfo.write(toFile: debugFile, atomically: true, encoding: .utf8)
        return nil
    }

    private func statusIcon(for status: DownloadItem.DownloadStatus) -> some View {
        Group {
            switch status {
            case .pending:
                Image(systemName: "clock")
                    .foregroundColor(.orange)
            case .downloading:
                ProgressView()
                    .scaleEffect(0.8)
            case .completed:
                Image(systemName: "checkmark.circle.fill")
                    .foregroundColor(.green)
            case .failed:
                Image(systemName: "xmark.circle.fill")
                    .foregroundColor(.red)
            case .cancelled:
                Image(systemName: "minus.circle.fill")
                    .foregroundColor(.gray)
            }
        }
        .font(.caption)
    }

    private func backgroundColorForStatus(_ status: DownloadItem.DownloadStatus) -> Color {
        switch status {
        case .pending:
            return Color.orange.opacity(0.1)
        case .downloading:
            return Color.blue.opacity(0.1)
        case .completed:
            return Color.green.opacity(0.1)
        case .failed:
            return Color.red.opacity(0.1)
        case .cancelled:
            return Color.gray.opacity(0.1)
        }
    }

    // MARK: - Action Methods
    private func stopAllDownloads() {
        downloadManager.stopAllDownloads()
        isDownloading = false
        statusMessage = "All downloads stopped"

        // Reset status after a delay
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            self.statusMessage = "Ready to download"
        }
    }

    private func downloadSingleVideo() {
        guard !quickVideoURL.isEmpty else { return }

        isDownloading = true
        statusMessage = "Starting quick download..."

        let manager = downloadManager
        manager.addToQueue(title: "Getting video info...", url: quickVideoURL, channelName: "Individual Videos")
        quickVideoURL = ""

        monitorDownloads()
    }

    private func addSourceToLibrary() {
        print("🔥 addSourceToLibrary() called!")
        print("🔥 newChannelURL: '\(newChannelURL)'")

        guard !newChannelURL.isEmpty else {
            print("🔥 URL is empty, returning")
            return
        }

        // Detect source type (channel or playlist)
        let sourceType = downloadManager.detectSourceType(from: newChannelURL)
        print("🔥 Detected source type: \(sourceType)")

        // Capture the URL, limits, and language before clearing
        let urlToProcess = newChannelURL
        let liveLimitToProcess = newChannelLiveLimit
        let regularLimitToProcess = newChannelRegularLimit
        let languageToProcess = newChannelLanguage

        print("🔥 About to process \(sourceType.displayName.lowercased()) with URL: \(urlToProcess)")
        statusMessage = "Adding \(sourceType.displayName.lowercased()) and fetching info..."

        // Clear the URL immediately to reset the UI
        newChannelURL = ""
        newChannelLiveLimit = 10
        newChannelRegularLimit = 10
        newChannelLanguage = .auto

        DispatchQueue.global().async {
            print("🔥 Inside DispatchQueue.global().async with URL: \(urlToProcess)")
            if sourceType == .playlist {
                self.fetchPlaylistInfoAndAdd(url: urlToProcess, maxVideos: liveLimitToProcess + regularLimitToProcess, language: languageToProcess)
            } else {
                self.fetchChannelThumbnailAndAdd(url: urlToProcess, liveLimit: liveLimitToProcess, regularLimit: regularLimitToProcess, language: languageToProcess)
            }
        }

        print("🔥 addSourceToLibrary() finished")
    }

    private func fetchChannelThumbnailAndAdd(url: String, liveLimit: Int, regularLimit: Int, language: PreferredLanguage = .auto) {
        print("🔥 fetchChannelThumbnailAndAdd called with URL: \(url)")

        // Capture language parameter for use in async blocks
        let capturedLanguage = language

        guard let scriptPath = findPythonScript("get_channel_thumbnail") else {
            print("🔥 Script not found!")
            DispatchQueue.main.async { [capturedLanguage] in
                let channelName = self.extractChannelName(from: url)
                self.downloadManager.saveChannel(name: channelName, url: url, videoCount: liveLimit + regularLimit, thumbnailURL: nil, liveVideoLimit: liveLimit, regularVideoLimit: regularLimit, preferredLanguage: capturedLanguage)
                self.statusMessage = "Channel '\(channelName)' added to library (script not found)"

                DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
                    self.statusMessage = "Ready to download"
                }
            }
            return
        }

        executeChannelScript(scriptPath: scriptPath, url: url, liveLimit: liveLimit, regularLimit: regularLimit, language: capturedLanguage)
    }

    private func executeChannelScript(scriptPath: String, url: String, liveLimit: Int, regularLimit: Int, language: PreferredLanguage) {
        print("=== EXECUTING CHANNEL SCRIPT ===")
        print("Script path: \(scriptPath)")
        print("URL: \(url)")
        print("Live limit: \(liveLimit)")
        print("Regular limit: \(regularLimit)")
        print("Script exists: \(FileManager.default.fileExists(atPath: scriptPath))")

        let task = Process()
        task.executableURL = URL(fileURLWithPath: "/usr/bin/python3")
        task.arguments = [scriptPath, url]

        // Separate pipes for stdout and stderr
        let outputPipe = Pipe()
        let errorPipe = Pipe()
        task.standardOutput = outputPipe
        task.standardError = errorPipe

        do {
            try task.run()
            task.waitUntilExit()

            // Read stdout (JSON data)
            let outputData = outputPipe.fileHandleForReading.readDataToEndOfFile()
            let output = String(data: outputData, encoding: .utf8) ?? ""

            // Read stderr (debug info)
            let errorData = errorPipe.fileHandleForReading.readDataToEndOfFile()
            let errorOutput = String(data: errorData, encoding: .utf8) ?? ""

            print("=== SCRIPT OUTPUT ===")
            print("Thumbnail script stdout: \(output)")
            print("Thumbnail script stderr: \(errorOutput)")
            print("=== END OUTPUT ===")

            DispatchQueue.main.async { [language] in
                // Parse JSON response
                if let jsonData = output.data(using: .utf8) {
                    print("JSON data created successfully")
                    if let result = try? JSONSerialization.jsonObject(with: jsonData) as? [String: Any] {
                        print("JSON parsed successfully: \(result)")
                        if let success = result["success"] as? Bool {
                            print("Success value: \(success)")
                            if success {
                                let channelName = result["channel_name"] as? String ?? self.extractChannelName(from: url)
                                let thumbnailURL = result["thumbnail_url"] as? String

                                print("Successfully parsed channel: \(channelName), thumbnail: \(thumbnailURL ?? "none")")

                                self.downloadManager.saveChannel(name: channelName, url: url, videoCount: liveLimit + regularLimit, thumbnailURL: thumbnailURL, liveVideoLimit: liveLimit, regularVideoLimit: regularLimit, preferredLanguage: language)
                                self.statusMessage = "Channel '\(channelName)' added to library"

                                // Auto-start download for newly added channel
                                if let newChannel = self.downloadManager.savedChannels.first(where: { $0.url == url }) {
                                    self.downloadChannelVideos(newChannel)
                                }
                            } else {
                                print("Success was false")
                                let channelName = self.extractChannelName(from: url)
                                self.downloadManager.saveChannel(name: channelName, url: url, videoCount: liveLimit + regularLimit, thumbnailURL: nil, liveVideoLimit: liveLimit, regularVideoLimit: regularLimit, preferredLanguage: language)
                                self.statusMessage = "Channel '\(channelName)' added to library (success=false)"

                                // Auto-start download for newly added channel
                                if let newChannel = self.downloadManager.savedChannels.first(where: { $0.url == url }) {
                                    self.downloadChannelVideos(newChannel)
                                }
                            }
                        } else {
                            print("No success field found")
                            let channelName = self.extractChannelName(from: url)
                            self.downloadManager.saveChannel(name: channelName, url: url, videoCount: liveLimit + regularLimit, thumbnailURL: nil, liveVideoLimit: liveLimit, regularVideoLimit: regularLimit, preferredLanguage: language)
                            self.statusMessage = "Channel '\(channelName)' added to library (no success field)"
                        }
                    } else {
                        print("Failed to parse JSON. Raw output: '\(output)'")
                        let channelName = self.extractChannelName(from: url)
                        self.downloadManager.saveChannel(name: channelName, url: url, videoCount: liveLimit + regularLimit, thumbnailURL: nil, liveVideoLimit: liveLimit, regularVideoLimit: regularLimit, preferredLanguage: language)
                        self.statusMessage = "Channel '\(channelName)' added to library (JSON parse failed)"
                    }
                } else {
                    print("Failed to create JSON data from output")
                    let channelName = self.extractChannelName(from: url)
                    self.downloadManager.saveChannel(name: channelName, url: url, videoCount: liveLimit + regularLimit, thumbnailURL: nil, liveVideoLimit: liveLimit, regularVideoLimit: regularLimit)
                    self.statusMessage = "Channel '\(channelName)' added to library (no JSON data)"
                }

                DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
                    self.statusMessage = "Ready to download"
                }
            }

        } catch {
            print("Process execution error: \(error)")
            DispatchQueue.main.async { [language] in
                let channelName = self.extractChannelName(from: url)
                self.downloadManager.saveChannel(name: channelName, url: url, videoCount: liveLimit + regularLimit, thumbnailURL: nil, liveVideoLimit: liveLimit, regularVideoLimit: regularLimit, preferredLanguage: language)
                self.statusMessage = "Channel '\(channelName)' added to library (script error)"

                DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
                    self.statusMessage = "Ready to download"
                }
            }
        }
    }

    private func fetchPlaylistInfoAndAdd(url: String, maxVideos: Int, language: PreferredLanguage = .auto) {
        print("🔥 fetchPlaylistInfoAndAdd called with URL: \(url)")

        downloadManager.getPlaylistVideos(url, maxVideos: maxVideos) { result in
            DispatchQueue.main.async {
                switch result {
                case .success(let videos):
                    let playlistName = self.extractPlaylistName(from: url)
                    print("Successfully fetched \(videos.count) videos from playlist: \(playlistName)")

                    self.downloadManager.saveChannel(
                        name: playlistName,
                        url: url,
                        videoCount: videos.count,
                        thumbnailURL: nil,
                        liveVideoLimit: maxVideos,
                        regularVideoLimit: 0,
                        sourceType: .playlist,
                        preferredLanguage: language
                    )

                    self.statusMessage = "Playlist '\(playlistName)' added to library"

                    // Auto-start download for newly added playlist
                    if let newPlaylist = self.downloadManager.savedChannels.first(where: { $0.url == url }) {
                        self.downloadPlaylistVideos(newPlaylist, videos: videos)
                    }

                case .failure(let error):
                    print("Failed to fetch playlist info: \(error)")
                    let playlistName = self.extractPlaylistName(from: url)
                    self.downloadManager.saveChannel(
                        name: playlistName,
                        url: url,
                        videoCount: maxVideos,
                        thumbnailURL: nil,
                        liveVideoLimit: maxVideos,
                        regularVideoLimit: 0,
                        sourceType: .playlist,
                        preferredLanguage: language
                    )
                    self.statusMessage = "Playlist '\(playlistName)' added to library (fetch failed)"
                }

                DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
                    self.statusMessage = "Ready to download"
                }
            }
        }
    }

    private func extractPlaylistName(from url: String) -> String {
        // Extract playlist ID and create a name
        if let listRange = url.range(of: "list=") {
            let listId = String(url[listRange.upperBound...]).components(separatedBy: "&").first ?? "Unknown"
            return "Playlist_\(listId)"
        }
        return "Unknown Playlist"
    }

    private func downloadPlaylistVideos(_ playlist: SavedChannel, videos: [VideoInfo]) {
        print("🔥 downloadPlaylistVideos called for playlist: \(playlist.name)")

        let manager = downloadManager

        for video in videos {
            print("🔥 Adding playlist video to queue: \(video.title)")
            manager.addToQueue(
                title: video.title,
                url: video.url,
                channelName: playlist.name,
                channelId: playlist.id
            )
        }

        self.statusMessage = "Playlist videos added to download queue..."
        self.monitorDownloads()
    }

    private func selectAllChannels() {
        if selectedChannels.count == downloadManager.savedChannels.count {
            selectedChannels.removeAll()
        } else {
            selectedChannels = Set(downloadManager.savedChannels.map { $0.id })
        }
    }

    private func downloadSelectedChannels() {
        guard !selectedChannels.isEmpty else { return }

        isDownloading = true
        statusMessage = "Starting download for \(selectedChannels.count) channels..."

        for channelId in selectedChannels {
            if let channel = downloadManager.savedChannels.first(where: { $0.id == channelId }) {
                downloadChannelVideos(channel)
            }
        }

        selectedChannels.removeAll()
        monitorDownloads()
    }

    private func downloadAllChannels() {
        guard !downloadManager.savedChannels.isEmpty else { return }

        isDownloading = true
        let channelCount = downloadManager.savedChannels.count
        statusMessage = "Starting download for all \(channelCount) channels..."

        // Process channels one by one to show progress
        processChannelsSequentially(downloadManager.savedChannels, currentIndex: 0)
    }

    private func processChannelsSequentially(_ channels: [SavedChannel], currentIndex: Int) {
        guard currentIndex < channels.count else {
            // All channels processed
            currentlyProcessingChannel = nil
            monitorDownloads()
            return
        }

        let channel = channels[currentIndex]
        currentlyProcessingChannel = channel.name
        statusMessage = "Processing channel \(currentIndex + 1) of \(channels.count): \(channel.name)"

        // Start download for this channel
        downloadChannelVideos(channel)

        // Wait a bit then process next channel
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            self.processChannelsSequentially(channels, currentIndex: currentIndex + 1)
        }
    }

    private func clearCompleted() {
        downloadManager.clearCompleted()
    }

    private func toggleChannelSelection(_ channelId: UUID) {
        if selectedChannels.contains(channelId) {
            selectedChannels.remove(channelId)
        } else {
            selectedChannels.insert(channelId)
        }
    }

    private func downloadChannel(_ channel: SavedChannel) {
        isDownloading = true
        statusMessage = "Downloading channel: \(channel.name)..."

        downloadChannelVideos(channel)
        monitorDownloads()
    }



    private func removeChannel(_ channel: SavedChannel) {
        downloadManager.removeChannel(channel.id)
    }

    private func smartDownloadChannel(_ channel: SavedChannel) {
        print("🔥 smartDownloadChannel called for: \(channel.name)")
        isDownloading = true
        statusMessage = "Updating and downloading channel: \(channel.name)..."

        // This will update the channel video list and then download
        downloadChannelVideos(channel)
        monitorDownloads()
    }



    private func showChannelFolder(_ channel: SavedChannel) {
        let channelDir = downloadManager.outputDirectory.appendingPathComponent(channel.name)

        // Create directory if it doesn't exist
        try? FileManager.default.createDirectory(at: channelDir, withIntermediateDirectories: true)

        // Open the folder
        NSWorkspace.shared.open(channelDir)
    }

    private func retryDownload(_ item: DownloadItem) {
        let manager = downloadManager
        manager.removeFromQueue(item.id)
        manager.addToQueue(title: item.title, url: item.url, channelName: item.channelName, channelId: item.channelId)
    }

    private func showFile(_ item: DownloadItem) {
        if let outputPath = item.outputPath {
            NSWorkspace.shared.selectFile(outputPath, inFileViewerRootedAtPath: "")
        }
    }

    private func downloadChannelVideos(_ channel: SavedChannel) {
        isDownloading = true
        statusMessage = "Fetching video list from \(channel.name)..."

        // Validate channel URL
        guard isValidYouTubeURL(channel.url) else {
            errorManager.handleError(AppError(
                title: "Invalid Channel URL",
                message: "The URL for '\(channel.name)' is not a valid YouTube channel URL.",
                recoveryAction: "Edit the channel and provide a valid YouTube channel URL",
                isRecoverable: true
            ))
            isDownloading = false
            return
        }

        // Get channel videos using Python script
        DispatchQueue.global().async {
            self.getChannelVideosAndDownload(channel)
        }
    }

    private func getChannelVideosAndDownload(_ channel: SavedChannel) {
        print("🔥 getChannelVideosAndDownload called for channel: \(channel.name)")

        guard let scriptPath = findPythonScript("get_channel_videos") else {
            print("🔥 get_channel_videos script not found!")
            errorManager.handleError(ProcessError.scriptNotFound("get_channel_videos"))
            isDownloading = false
            return
        }

        print("🔥 Found get_channel_videos script at: \(scriptPath)")

        let task = Process()
        task.executableURL = URL(fileURLWithPath: "/usr/bin/python3")
        task.arguments = [scriptPath, channel.url, String(channel.liveVideoLimit), String(channel.regularVideoLimit)]

        // Separate pipes for stdout and stderr
        let outputPipe = Pipe()
        let errorPipe = Pipe()
        task.standardOutput = outputPipe
        task.standardError = errorPipe

        do {
            try task.run()
            task.waitUntilExit()

            // Read stdout (JSON data)
            let outputData = outputPipe.fileHandleForReading.readDataToEndOfFile()
            let output = String(data: outputData, encoding: .utf8) ?? ""

            // Read stderr (debug info)
            let errorData = errorPipe.fileHandleForReading.readDataToEndOfFile()
            let errorOutput = String(data: errorData, encoding: .utf8) ?? ""

            // Print debug info for troubleshooting
            print("Python script output (stdout): \(output)")
            print("Python script errors (stderr): \(errorOutput)")

            // Try to parse JSON from stdout only
            if let jsonData = output.data(using: .utf8),
               let result = try? JSONSerialization.jsonObject(with: jsonData) as? [String: Any] {

                let manager = downloadManager // Capture the manager
                DispatchQueue.main.async {
                    if let success = result["success"] as? Bool, success,
                       let liveVideos = result["live_videos"] as? [[String: Any]],
                       let regularVideos = result["regular_videos"] as? [[String: Any]] {

                        let totalVideos = liveVideos.count + regularVideos.count
                        let liveToProcess = channel.downloadLiveVideos ? liveVideos.count : 0
                        let regularToProcess = channel.downloadRegularVideos ? regularVideos.count : 0
                        let totalToProcess = liveToProcess + regularToProcess

                        self.statusMessage = "Found \(totalVideos) videos (\(liveVideos.count) live, \(regularVideos.count) regular). Processing \(totalToProcess) videos..."

                        // Add live videos to download queue (only if enabled for this channel)
                        if !liveVideos.isEmpty && channel.downloadLiveVideos {
                            self.statusMessage = "Processing \(liveVideos.count) live videos..."
                            for video in liveVideos {
                                if let url = video["url"] as? String,
                                   let title = video["title"] as? String {
                                    print("🔥 Adding LIVE video to queue: \(title) from \(channel.name)")
                                    // Pass channel name without "/live" suffix - the Python script will handle the [LIVE] prefix
                                    let channelName = manager.useFolders ? "\(channel.name)/live" : channel.name
                                    manager.addToQueue(
                                        title: title,
                                        url: url,
                                        channelName: channelName,
                                        channelId: channel.id
                                    )
                                }
                            }
                        } else if !liveVideos.isEmpty && !channel.downloadLiveVideos {
                            print("🔥 Skipping \(liveVideos.count) live videos for \(channel.name) (disabled)")
                        }

                        // Add regular videos to download queue (only if enabled for this channel)
                        if !regularVideos.isEmpty && channel.downloadRegularVideos {
                            self.statusMessage = "Processing \(regularVideos.count) regular videos..."
                            for video in regularVideos {
                                if let url = video["url"] as? String,
                                   let title = video["title"] as? String {
                                    print("🔥 Adding REGULAR video to queue: \(title) from \(channel.name)")
                                    manager.addToQueue(
                                        title: title,
                                        url: url,
                                        channelName: channel.name,
                                        channelId: channel.id
                                    )
                                }
                            }
                        } else if !regularVideos.isEmpty && !channel.downloadRegularVideos {
                            print("🔥 Skipping \(regularVideos.count) regular videos for \(channel.name) (disabled)")
                        }

                        // Update status to indicate transcript downloads are starting
                        self.statusMessage = "Video list fetching completed. Starting transcript downloads..."
                        // Clear the processing indicator for this channel
                        if self.currentlyProcessingChannel == channel.name {
                            self.currentlyProcessingChannel = nil
                        }
                        self.monitorDownloads()

                    } else if let error = result["error"] as? String {
                        self.statusMessage = "Failed to get video list from \(channel.name): \(error)"
                        self.isDownloading = false

                        // Add a failed item to the queue to show the error
                        manager.addToQueue(
                            title: "❌ Failed to get video list",
                            url: channel.url,
                            channelName: channel.name,
                            status: .failed
                        )
                    } else {
                        self.statusMessage = "Failed to get video list from \(channel.name): Invalid response format"
                        self.isDownloading = false

                        // Add a failed item to the queue to show the error
                        manager.addToQueue(
                            title: "❌ Failed to get video list",
                            url: channel.url,
                            channelName: channel.name,
                            status: .failed
                        )
                    }
                }
            } else {
                DispatchQueue.main.async {
                    // Show more detailed error information
                    let errorMsg = errorOutput.isEmpty ? "Failed to parse channel videos - no valid JSON output" : "Script error: \(errorOutput)"
                    self.statusMessage = "Error: \(errorMsg)"
                    self.isDownloading = false
                    print("JSON parsing failed. Raw stdout: '\(output)'")
                    print("Raw stderr: '\(errorOutput)'")
                }
            }

        } catch {
            errorManager.handleError(error, context: "Channel video download for \(channel.name)")
            DispatchQueue.main.async {
                self.statusMessage = "Error: \(error.localizedDescription)"
                self.isDownloading = false
            }
        }
    }

    private func extractChannelName(from url: String) -> String {
        // Extract channel name from URL
        if let urlComponents = URLComponents(string: url),
           let path = urlComponents.path.split(separator: "/").last {
            return String(path).replacingOccurrences(of: "@", with: "")
        }
        return "Unknown Channel"
    }

    private func isValidYouTubeURL(_ url: String) -> Bool {
        guard let urlComponents = URLComponents(string: url),
              let host = urlComponents.host else {
            return false
        }

        let validHosts = ["youtube.com", "www.youtube.com", "m.youtube.com"]
        return validHosts.contains(host) &&
               (url.contains("/channel/") || url.contains("/c/") || url.contains("/@"))
    }

    private func formatDate(_ date: Date) -> String {
        let formatter = RelativeDateTimeFormatter()
        formatter.dateTimeStyle = .named
        return formatter.localizedString(for: date, relativeTo: Date())
    }

    private func selectOutputFolder() {
        let panel = NSOpenPanel()
        panel.canChooseFiles = false
        panel.canChooseDirectories = true
        panel.allowsMultipleSelection = false

        if panel.runModal() == .OK, let url = panel.url {
            downloadManager.setOutputDirectory(url)
        }
    }

    private func exportChannels() {
        guard let exportURL = downloadManager.exportChannels() else {
            statusMessage = "Failed to export channels"
            return
        }

        // Show the exported file in Finder
        NSWorkspace.shared.selectFile(exportURL.path, inFileViewerRootedAtPath: "")
        statusMessage = "Channels exported successfully"

        DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
            statusMessage = "Ready to download"
        }
    }

    private func importChannels() {
        let panel = NSOpenPanel()
        panel.canChooseFiles = true
        panel.canChooseDirectories = false
        panel.allowsMultipleSelection = false
        panel.allowedContentTypes = [.json]
        panel.prompt = "Import Channels"
        panel.message = "Select a channels export file to import"

        if panel.runModal() == .OK, let url = panel.url {
            let result = downloadManager.importChannels(from: url)

            if result.success {
                if result.duplicateCount > 0 {
                    statusMessage = "Imported \(result.importedCount) channels, \(result.duplicateCount) duplicates skipped"
                } else {
                    statusMessage = "Imported \(result.importedCount) channels successfully"
                }
            } else {
                statusMessage = "Import failed: \(result.error ?? "Unknown error")"
            }

            DispatchQueue.main.asyncAfter(deadline: .now() + 4.0) {
                statusMessage = "Ready to download"
            }
        }
    }

    private func installDependencies() {
        isDownloading = true
        statusMessage = "Installing Python dependencies..."

        DispatchQueue.global().async {
            let task = Process()
            task.executableURL = URL(fileURLWithPath: "/usr/bin/python3")

            guard let scriptPath = self.findPythonScript("install_dependencies") else {
                DispatchQueue.main.async {
                    self.statusMessage = "Error: Install dependencies script not found"
                    self.isDownloading = false
                }
                return
            }

            task.arguments = [scriptPath]

            let pipe = Pipe()
            task.standardOutput = pipe
            task.standardError = pipe

            do {
                try task.run()
                task.waitUntilExit()

                let data = pipe.fileHandleForReading.readDataToEndOfFile()
                let output = String(data: data, encoding: .utf8) ?? ""

                DispatchQueue.main.async {
                    self.isDownloading = false

                    if task.terminationStatus == 0 {
                        self.statusMessage = "Dependencies installed successfully!"
                    } else {
                        self.statusMessage = "Failed to install dependencies"
                        print("Install error: \(output)")
                    }

                    DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
                        self.statusMessage = "Ready to download"
                    }
                }
            } catch {
                DispatchQueue.main.async {
                    self.isDownloading = false
                    self.statusMessage = "Failed to run installer: \(error.localizedDescription)"

                    DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
                        self.statusMessage = "Ready to download"
                    }
                }
            }
        }
    }

    private func monitorDownloads() {
        Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { timer in
            let activeDownloads = downloadManager.downloadQueue.filter {
                $0.status == .downloading || $0.status == .pending
            }

            if activeDownloads.isEmpty {
                timer.invalidate()
                isDownloading = false

                let completedCount = downloadManager.downloadQueue.filter { $0.status == .completed }.count
                let failedCount = downloadManager.downloadQueue.filter { $0.status == .failed }.count

                // Update last downloaded date for all channels that had downloads
                let channelsWithDownloads = Set(downloadManager.downloadQueue.compactMap { $0.channelName })
                for channelName in channelsWithDownloads {
                    if let channel = downloadManager.savedChannels.first(where: { $0.name == channelName || channelName.hasPrefix($0.name) }) {
                        downloadManager.updateChannelLastDownloaded(channel.id)
                    }
                }

                if failedCount > 0 {
                    statusMessage = "Transcript downloads complete: \(completedCount) successful, \(failedCount) failed"
                } else if completedCount > 0 {
                    statusMessage = "Transcript downloads complete: \(completedCount) transcripts downloaded successfully!"
                } else {
                    // No actual downloads happened, probably just video list fetching
                    statusMessage = "Video list processing complete. No new transcripts to download."
                }

                DispatchQueue.main.asyncAfter(deadline: .now() + 5.0) {
                    statusMessage = "Ready to download"
                    currentlyProcessingChannel = nil
                }
            } else {
                let downloadingCount = activeDownloads.filter { $0.status == .downloading }.count
                let pendingCount = activeDownloads.filter { $0.status == .pending }.count

                if downloadingCount > 0 {
                    statusMessage = "Downloading \(downloadingCount) transcripts..."
                } else {
                    statusMessage = "Preparing \(pendingCount) downloads..."
                }
            }
        }
    }

    private func saveChannelChanges() {
        guard let channel = editingChannel else { return }

        // Update the channel with new values
        let updatedChannel = SavedChannel(
            id: channel.id,
            name: editChannelName.trimmingCharacters(in: .whitespacesAndNewlines),
            url: editChannelURL.trimmingCharacters(in: .whitespacesAndNewlines),
            videoCount: max(1, editLiveLimit + editRegularLimit),
            dateAdded: channel.dateAdded,
            thumbnailURL: channel.thumbnailURL,
            liveVideoLimit: max(1, editLiveLimit),
            regularVideoLimit: max(1, editRegularLimit),
            lastDownloaded: channel.lastDownloaded,
            sourceType: channel.sourceType,
            preferredLanguage: editPreferredLanguage,
            downloadLiveVideos: editDownloadLiveVideos,
            downloadRegularVideos: editDownloadRegularVideos
        )

        // Update in the download manager
        downloadManager.updateChannel(updatedChannel)

        // Close the sheet
        showEditChannelSheet = false
        editingChannel = nil

        // Clear edit fields
        editChannelName = ""
        editChannelURL = ""
        editLiveLimit = 10
        editRegularLimit = 10
        editPreferredLanguage = .auto
        editDownloadLiveVideos = true
        editDownloadRegularVideos = true
    }

    private func checkAndInstallPytubefix() {
        statusMessage = "Checking dependencies..."

        Task {
            await checkAndUpdateAllDependencies()
        }
    }

    private func checkAndUpdateAllDependencies() async {
        let dependencies = [
            "pytubefix>=9.4.1",
            "requests>=2.25.0"
        ]

        for dependency in dependencies {
            let packageName = String(dependency.split(separator: ">").first ?? "")

            await MainActor.run {
                statusMessage = "Checking \(packageName)..."
            }

            // Check if package needs update
            if await needsUpdate(packageName) {
                await MainActor.run {
                    statusMessage = "Updating \(packageName)..."
                }

                await updateDependency(dependency)
            }
        }

        await MainActor.run {
            statusMessage = "Dependencies up to date!"

            DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
                self.statusMessage = "Ready to download"
            }
        }
    }

    private func needsUpdate(_ packageName: String) async -> Bool {
        // Check if package is installed
        let checkTask = Process()
        checkTask.executableURL = URL(fileURLWithPath: "/usr/bin/python3")
        checkTask.arguments = ["-c", "import \(packageName)"]

        do {
            try checkTask.run()
            checkTask.waitUntilExit()

            if checkTask.terminationStatus != 0 {
                // Package not installed
                return true
            }

            // Check for updates weekly
            let lastCheckKey = "lastUpdate_\(packageName)"
            let lastCheck = UserDefaults.standard.object(forKey: lastCheckKey) as? Date ?? Date.distantPast
            let daysSinceLastCheck = Calendar.current.dateComponents([.day], from: lastCheck, to: Date()).day ?? 0

            if daysSinceLastCheck >= 7 {
                UserDefaults.standard.set(Date(), forKey: lastCheckKey)
                return true
            }

            return false
        } catch {
            return true
        }
    }

    private func updateDependency(_ dependency: String) async {
        let task = Process()
        task.executableURL = URL(fileURLWithPath: "/usr/bin/python3")
        task.arguments = ["-m", "pip", "install", "--upgrade", dependency]

        let pipe = Pipe()
        task.standardOutput = pipe
        task.standardError = pipe

        do {
            try task.run()
            task.waitUntilExit()

            if task.terminationStatus == 0 {
                print("✅ Updated \(dependency)")
            } else {
                let data = pipe.fileHandleForReading.readDataToEndOfFile()
                let error = String(data: data, encoding: .utf8) ?? "Unknown error"
                print("❌ Failed to update \(dependency): \(error)")

                // Try fallback installation
                await fallbackInstall(dependency)
            }
        } catch {
            print("❌ Error updating \(dependency): \(error)")
            await fallbackInstall(dependency)
        }
    }

    private func fallbackInstall(_ dependency: String) async {
        // Try installing without upgrade flag
        let task = Process()
        task.executableURL = URL(fileURLWithPath: "/usr/bin/python3")
        task.arguments = ["-m", "pip", "install", dependency]

        do {
            try task.run()
            task.waitUntilExit()

            if task.terminationStatus == 0 {
                print("✅ Installed \(dependency) via fallback")
            }
        } catch {
            print("❌ Fallback install failed for \(dependency)")
        }
    }

    private func installPytubefixOnStartup() {
        DispatchQueue.main.async {
            self.statusMessage = "Installing dependencies... Please wait"
        }

        let installTask = Process()
        installTask.executableURL = URL(fileURLWithPath: "/usr/bin/python3")
        installTask.arguments = ["-m", "pip", "install", "pytubefix>=9.4.1"]

        let pipe = Pipe()
        installTask.standardOutput = pipe
        installTask.standardError = pipe

        do {
            try installTask.run()
            installTask.waitUntilExit()

            DispatchQueue.main.async {
                if installTask.terminationStatus == 0 {
                    self.statusMessage = "Dependencies installed successfully!"
                    print("✅ pytubefix installed successfully on startup")

                    DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
                        self.statusMessage = "Ready to download"
                    }
                } else {
                    self.statusMessage = "Failed to install dependencies. Some features may not work."
                    print("❌ Failed to install pytubefix on startup")

                    DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
                        self.statusMessage = "Ready to download"
                    }
                }
            }
        } catch {
            DispatchQueue.main.async {
                self.statusMessage = "Error installing dependencies: \(error.localizedDescription)"
                print("❌ Error installing pytubefix: \(error)")

                DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
                    self.statusMessage = "Ready to download"
                }
            }
        }
    }
}

#Preview {
    ContentView()
}
