import SwiftUI

// Simple error manager for production
class ErrorManager: ObservableObject {
    @Published var currentError: AppError?
    @Published var showErrorAlert = false

    func handleError(_ error: Error, context: String = "") {
        let appError = AppError.from(error, context: context)

        DispatchQueue.main.async {
            self.currentError = appError
            self.showErrorAlert = true
        }

        // Log error
        print("❌ ERROR [\(context)]: \(appError.message)")
    }

    func handleError(_ appError: AppError) {
        DispatchQueue.main.async {
            self.currentError = appError
            self.showErrorAlert = true
        }

        print("❌ ERROR: \(appError.message)")
    }

    func clearError() {
        currentError = nil
        showErrorAlert = false
    }
}

struct AppError: Identifiable, LocalizedError {
    let id = UUID()
    let title: String
    let message: String
    let recoveryAction: String?
    let isRecoverable: Bool

    var errorDescription: String? {
        return message
    }

    static func from(_ error: Error, context: String = "") -> AppError {
        let contextPrefix = context.isEmpty ? "" : "[\(context)] "

        if let appError = error as? AppError {
            return appError
        }

        // Generic error
        return AppError(
            title: "Error",
            message: "\(contextPrefix)\(error.localizedDescription)",
            recoveryAction: "Please try again",
            isRecoverable: true
        )
    }
}

enum ProcessError: LocalizedError {
    case scriptNotFound(String)
    case executionFailed(String, Int)

    var localizedDescription: String {
        switch self {
        case .scriptNotFound(let script):
            return "Required script '\(script)' not found"
        case .executionFailed(let script, let code):
            return "Script '\(script)' failed with exit code \(code)"
        }
    }
}

@main
struct YouTubeTranscriptDownloaderApp: App {
    @StateObject private var errorManager = ErrorManager()

    var body: some Scene {
        WindowGroup {
            ContentView()
                .environmentObject(errorManager)
        }
    }
}
