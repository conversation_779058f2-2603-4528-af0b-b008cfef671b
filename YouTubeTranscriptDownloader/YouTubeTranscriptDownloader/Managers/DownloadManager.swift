import Foundation
import SwiftUI

struct VideoInfo: Codable {
    let title: String
    let url: String
    let videoId: String
    let author: String
    let duration: Int?
    let viewCount: Int?
    let publishDate: String?
}

enum SourceType: String, Codable, CaseIterable {
    case channel = "channel"
    case playlist = "playlist"

    var displayName: String {
        switch self {
        case .channel:
            return "Channel"
        case .playlist:
            return "Playlist"
        }
    }
}

enum PreferredLanguage: String, Codable, CaseIterable {
    case auto = "auto"
    case english = "en"
    case hindi = "hi"
    case spanish = "es"
    case french = "fr"
    case german = "de"
    case italian = "it"
    case portuguese = "pt"
    case russian = "ru"
    case japanese = "ja"
    case korean = "ko"
    case chinese = "zh"
    case arabic = "ar"

    var displayName: String {
        switch self {
        case .auto:
            return "Auto (English preferred)"
        case .english:
            return "English"
        case .hindi:
            return "Hindi (हिन्दी)"
        case .spanish:
            return "Spanish (Español)"
        case .french:
            return "French (Français)"
        case .german:
            return "German (Deutsch)"
        case .italian:
            return "Italian (Italiano)"
        case .portuguese:
            return "Portuguese (Português)"
        case .russian:
            return "Russian (Русский)"
        case .japanese:
            return "Japanese (日本語)"
        case .korean:
            return "Korean (한국어)"
        case .chinese:
            return "Chinese (中文)"
        case .arabic:
            return "Arabic (العربية)"
        }
    }

    var languageCodes: [String] {
        switch self {
        case .auto:
            return ["en", "a.en", "en-US", "en-GB"]
        case .english:
            return ["en", "a.en", "en-US", "en-GB"]
        case .hindi:
            return ["hi", "hi-IN", "a.hi"]
        case .spanish:
            return ["es", "es-ES", "es-MX", "a.es"]
        case .french:
            return ["fr", "fr-FR", "a.fr"]
        case .german:
            return ["de", "de-DE", "a.de"]
        case .italian:
            return ["it", "it-IT", "a.it"]
        case .portuguese:
            return ["pt", "pt-BR", "pt-PT", "a.pt"]
        case .russian:
            return ["ru", "ru-RU", "a.ru"]
        case .japanese:
            return ["ja", "ja-JP", "a.ja"]
        case .korean:
            return ["ko", "ko-KR", "a.ko"]
        case .chinese:
            return ["zh", "zh-CN", "zh-TW", "a.zh"]
        case .arabic:
            return ["ar", "ar-SA", "a.ar"]
        }
    }
}

struct SavedChannel: Codable, Identifiable {
    let id: UUID
    let name: String
    let url: String
    let videoCount: Int // Keep for backward compatibility
    let liveVideoLimit: Int
    let regularVideoLimit: Int
    let dateAdded: Date
    let lastDownloaded: Date?
    let thumbnailURL: String?
    let sourceType: SourceType
    let preferredLanguage: PreferredLanguage

    // Custom decoder for backward compatibility
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)

        id = try container.decode(UUID.self, forKey: .id)
        name = try container.decode(String.self, forKey: .name)
        url = try container.decode(String.self, forKey: .url)
        videoCount = try container.decode(Int.self, forKey: .videoCount)
        dateAdded = try container.decode(Date.self, forKey: .dateAdded)
        lastDownloaded = try container.decodeIfPresent(Date.self, forKey: .lastDownloaded)
        thumbnailURL = try container.decodeIfPresent(String.self, forKey: .thumbnailURL)

        // Handle video limits with backward compatibility
        if let liveLimit = try container.decodeIfPresent(Int.self, forKey: .liveVideoLimit) {
            liveVideoLimit = liveLimit
        } else {
            liveVideoLimit = videoCount
        }

        if let regularLimit = try container.decodeIfPresent(Int.self, forKey: .regularVideoLimit) {
            regularVideoLimit = regularLimit
        } else {
            regularVideoLimit = videoCount
        }

        // New fields with defaults for backward compatibility
        sourceType = try container.decodeIfPresent(SourceType.self, forKey: .sourceType) ?? .channel
        preferredLanguage = try container.decodeIfPresent(PreferredLanguage.self, forKey: .preferredLanguage) ?? .auto
    }

    private enum CodingKeys: String, CodingKey {
        case id, name, url, videoCount, liveVideoLimit, regularVideoLimit
        case dateAdded, lastDownloaded, thumbnailURL, sourceType, preferredLanguage
    }

    init(name: String, url: String, videoCount: Int, dateAdded: Date, thumbnailURL: String? = nil, liveVideoLimit: Int? = nil, regularVideoLimit: Int? = nil, lastDownloaded: Date? = nil, sourceType: SourceType = .channel, preferredLanguage: PreferredLanguage = .auto) {
        self.id = UUID()
        self.name = name
        self.url = url
        self.videoCount = videoCount
        self.liveVideoLimit = liveVideoLimit ?? videoCount
        self.regularVideoLimit = regularVideoLimit ?? videoCount
        self.dateAdded = dateAdded
        self.lastDownloaded = lastDownloaded
        self.thumbnailURL = thumbnailURL
        self.sourceType = sourceType
        self.preferredLanguage = preferredLanguage
    }

    // Custom initializer with id (for updates and backward compatibility)
    init(id: UUID, name: String, url: String, videoCount: Int, dateAdded: Date, thumbnailURL: String? = nil, liveVideoLimit: Int? = nil, regularVideoLimit: Int? = nil, lastDownloaded: Date? = nil, sourceType: SourceType = .channel, preferredLanguage: PreferredLanguage = .auto) {
        self.id = id
        self.name = name
        self.url = url
        self.videoCount = videoCount
        self.liveVideoLimit = liveVideoLimit ?? videoCount
        self.regularVideoLimit = regularVideoLimit ?? videoCount
        self.dateAdded = dateAdded
        self.lastDownloaded = lastDownloaded
        self.thumbnailURL = thumbnailURL
        self.sourceType = sourceType
        self.preferredLanguage = preferredLanguage
    }
}

struct DownloadItem: Identifiable, Hashable {
    let id = UUID()
    var title: String
    var url: String
    var channelName: String
    var status: DownloadStatus
    var progress: Double
    var error: String?
    var outputPath: String?
    var channelId: UUID? // To link back to the source channel/playlist

    enum DownloadStatus {
        case pending
        case downloading
        case completed
        case failed
        case cancelled
    }
}

class DownloadManager: ObservableObject {
    @Published var downloadQueue: [DownloadItem] = []
    @Published var outputDirectory: URL
    @Published var savedChannels: [SavedChannel] = []
    @Published var includeTimestamps: Bool = false

    var queueCount: Int {
        downloadQueue.filter { $0.status == .pending || $0.status == .downloading }.count
    }
    
    init() {
        // Default output directory
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
        self.outputDirectory = documentsPath.appendingPathComponent("YouTube Transcripts")
        
        // Create output directory if it doesn't exist
        try? FileManager.default.createDirectory(at: outputDirectory, withIntermediateDirectories: true)
        
        // Load saved output directory preference
        loadOutputDirectory()

        // Load saved channels
        loadSavedChannels()
    }
    
    func setOutputDirectory(_ url: URL) {
        outputDirectory = url
        saveOutputDirectory()

        // Create directory if it doesn't exist
        try? FileManager.default.createDirectory(at: outputDirectory, withIntermediateDirectories: true)
    }

    private var appSupportDirectory: URL {
        let appSupportPath = FileManager.default.urls(for: .applicationSupportDirectory, in: .userDomainMask).first!
        let appDirectory = appSupportPath.appendingPathComponent("YouTubeTranscriptDownloader")

        // Create app support directory if it doesn't exist
        try? FileManager.default.createDirectory(at: appDirectory, withIntermediateDirectories: true)

        return appDirectory
    }

    private var settingsFileURL: URL {
        return appSupportDirectory.appendingPathComponent("settings.json")
    }

    private func loadOutputDirectory() {
        // First try to load from file
        if FileManager.default.fileExists(atPath: settingsFileURL.path) {
            do {
                let data = try Data(contentsOf: settingsFileURL)
                if let settings = try JSONSerialization.jsonObject(with: data) as? [String: String],
                   let pathString = settings["OutputDirectory"],
                   let url = URL(string: pathString) {
                    self.outputDirectory = url
                    return
                }
            } catch {
                print("Error loading settings from file: \(error)")
            }
        }

        // Fallback to UserDefaults
        if let savedPath = UserDefaults.standard.string(forKey: "OutputDirectory"),
           let url = URL(string: savedPath) {
            self.outputDirectory = url
            // Migrate to file storage
            saveOutputDirectory()
        }
    }

    private func saveOutputDirectory() {
        let settings = ["OutputDirectory": outputDirectory.absoluteString]

        do {
            let data = try JSONSerialization.data(withJSONObject: settings)
            try data.write(to: settingsFileURL)

            // Also save to UserDefaults (backup)
            UserDefaults.standard.set(outputDirectory.absoluteString, forKey: "OutputDirectory")
        } catch {
            print("Error saving settings: \(error)")
        }
    }
    
    func addToQueue(title: String, url: String, channelName: String, channelId: UUID? = nil, status: DownloadItem.DownloadStatus = .pending) {
        let item = DownloadItem(
            title: title,
            url: url,
            channelName: channelName,
            status: status,
            progress: status == .failed ? 0.0 : 0.0,
            channelId: channelId
        )
        downloadQueue.append(item)

        // Only start download if status is pending
        if status == .pending {
            DispatchQueue.global().async {
                self.performDownload(item: item)
            }
        }
    }

    private func performDownload(item: DownloadItem) {
        print("🔥 performDownload called for: \(item.title)")
        print("🔥 Video URL: \(item.url)")

        // Update status to downloading
        DispatchQueue.main.async {
            if let index = self.downloadQueue.firstIndex(where: { $0.id == item.id }) {
                self.downloadQueue[index].status = .downloading
                print("🔥 Status updated to downloading")
            }
        }

        // Create output directory
        let channelDir = outputDirectory.appendingPathComponent(item.channelName)
        try? FileManager.default.createDirectory(at: channelDir, withIntermediateDirectories: true)

        // Call Python script to download transcript
        let task = Process()
        task.executableURL = URL(fileURLWithPath: "/usr/bin/python3")

        // Try multiple locations for the enhanced Python script
        let scriptName = "download_transcript_enhanced"
        let bundlePath = Bundle.main.bundlePath
        print("🔥 Looking for script: \(scriptName)")
        print("🔥 Bundle path: \(bundlePath)")

        let possiblePaths = [
            // Bundled resource
            Bundle.main.path(forResource: scriptName, ofType: "py"),
            // Development location - direct path
            "/Users/<USER>/Downloads/Apps/Scripture 2/YouTubeTranscriptDownloader/YouTubeTranscriptDownloader/Python/\(scriptName).py",
            // Development location - relative to bundle
            bundlePath.replacingOccurrences(of: "/Build/Products/Debug/YouTubeTranscriptDownloader.app", with: "/YouTubeTranscriptDownloader/Python/\(scriptName).py"),
            // Alternative development location
            bundlePath.replacingOccurrences(of: "/Library/Developer/Xcode/DerivedData", with: "/Downloads/Apps/Scripture 2") + "/YouTubeTranscriptDownloader/Python/\(scriptName).py"
        ]

        var scriptPath = ""
        for path in possiblePaths {
            if let path = path, FileManager.default.fileExists(atPath: path) {
                scriptPath = path
                print("Found \(scriptName).py at: \(path)")
                break
            } else {
                print("Not found at: \(path ?? "nil")")
            }
        }

        // Verify script exists
        if scriptPath.isEmpty || !FileManager.default.fileExists(atPath: scriptPath) {
            print("ERROR: Enhanced Python script not found. Tried all paths.")
            DispatchQueue.main.async {
                if let index = self.downloadQueue.firstIndex(where: { $0.id == item.id }) {
                    self.downloadQueue[index].status = .failed
                    self.downloadQueue[index].error = "Enhanced download script not found"
                }
            }
            return
        }

        // Get language preference for this channel
        var languageCodes = ""
        if let channelId = item.channelId,
           let channel = savedChannels.first(where: { $0.id == channelId }) {
            languageCodes = channel.preferredLanguage.languageCodes.joined(separator: ",")
        }

        task.arguments = [scriptPath, item.url, channelDir.path, item.title, includeTimestamps ? "true" : "false", languageCodes]

        print("Executing command: /usr/bin/python3 \(task.arguments?.joined(separator: " ") ?? "")")
        print("Working directory: \(FileManager.default.currentDirectoryPath)")

        // Separate pipes for stdout and stderr
        let outputPipe = Pipe()
        let errorPipe = Pipe()
        task.standardOutput = outputPipe
        task.standardError = errorPipe

        do {
            try task.run()
            task.waitUntilExit()

            // Read stdout (JSON data)
            let outputData = outputPipe.fileHandleForReading.readDataToEndOfFile()
            let output = String(data: outputData, encoding: .utf8) ?? ""

            // Read stderr (debug info)
            let errorData = errorPipe.fileHandleForReading.readDataToEndOfFile()
            let errorOutput = String(data: errorData, encoding: .utf8) ?? ""

            print("Python script exit code: \(task.terminationStatus)")
            print("Python script stdout: \(output)")
            print("Python script stderr: \(errorOutput)")

            if task.terminationStatus == 0 {
                // Parse JSON response from stdout only
                if let jsonData = output.data(using: .utf8),
                   let response = try? JSONSerialization.jsonObject(with: jsonData) as? [String: Any],
                   let success = response["success"] as? Bool,
                   success {

                    // Get output path (try both "filepath" and "output_path" for compatibility)
                    let outputPath = response["filepath"] as? String ?? response["output_path"] as? String ?? ""

                    // Update with real video title if available
                    let realTitle = response["title"] as? String ?? response["video_title"] as? String ?? item.title

                    DispatchQueue.main.async {
                        if let index = self.downloadQueue.firstIndex(where: { $0.id == item.id }) {
                            self.downloadQueue[index].status = .completed
                            self.downloadQueue[index].progress = 1.0
                            self.downloadQueue[index].outputPath = outputPath
                            self.downloadQueue[index].title = realTitle
                        }
                    }
                } else {
                    // Parse error from JSON if available
                    var errorMessage = "Download failed"
                    if let jsonData = output.data(using: .utf8),
                       let response = try? JSONSerialization.jsonObject(with: jsonData) as? [String: Any],
                       let error = response["error"] as? String {
                        errorMessage = error
                    } else {
                        errorMessage = "Failed to parse response: \(output)"
                    }

                    print("Download failed: \(errorMessage)")
                    print("Raw stderr (debug info): \(errorOutput)")

                    DispatchQueue.main.async {
                        if let index = self.downloadQueue.firstIndex(where: { $0.id == item.id }) {
                            self.downloadQueue[index].status = .failed
                            self.downloadQueue[index].error = errorMessage
                        }
                    }
                }
            } else {
                let errorMessage = "Script execution failed (exit code: \(task.terminationStatus)): \(output)"
                print("Script execution failed: \(errorMessage)")

                DispatchQueue.main.async {
                    if let index = self.downloadQueue.firstIndex(where: { $0.id == item.id }) {
                        self.downloadQueue[index].status = .failed
                        self.downloadQueue[index].error = errorMessage
                    }
                }
            }
        } catch {
            // Failed
            DispatchQueue.main.async {
                if let index = self.downloadQueue.firstIndex(where: { $0.id == item.id }) {
                    self.downloadQueue[index].status = .failed
                    self.downloadQueue[index].error = error.localizedDescription
                }
            }
        }
    }

    private func sanitizeFileName(_ name: String) -> String {
        let invalidChars = CharacterSet(charactersIn: ":/\\?%*|\"<>")
        return name.components(separatedBy: invalidChars).joined(separator: "_")
    }
    
    func removeFromQueue(_ item: DownloadItem) {
        downloadQueue.removeAll { $0.id == item.id }
    }

    func retryDownload(_ item: DownloadItem) {
        if let index = downloadQueue.firstIndex(where: { $0.id == item.id }) {
            downloadQueue[index].status = .pending
            downloadQueue[index].progress = 0.0
            downloadQueue[index].error = nil

            DispatchQueue.global().async {
                self.performDownload(item: self.downloadQueue[index])
            }
        }
    }

    func clearCompleted() {
        downloadQueue.removeAll { $0.status == .completed }
    }

    func stopAllDownloads() {
        // Cancel all pending and downloading items
        for index in downloadQueue.indices {
            if downloadQueue[index].status == .pending || downloadQueue[index].status == .downloading {
                downloadQueue[index].status = .cancelled
            }
        }

        // Note: We can't easily stop running Python processes, but we mark them as cancelled
        // The UI will show them as cancelled and they won't be retried
        print("All downloads stopped/cancelled")
    }

    func clearQueue() {
        downloadQueue.removeAll()
        print("Download queue cleared")
    }
    
    func showOutputFolder() {
        NSWorkspace.shared.open(outputDirectory)
    }

    func removeFromQueue(_ id: UUID) {
        downloadQueue.removeAll { $0.id == id }
    }

    func saveChannel(name: String, url: String, videoCount: Int, thumbnailURL: String? = nil, liveVideoLimit: Int? = nil, regularVideoLimit: Int? = nil, sourceType: SourceType = .channel, preferredLanguage: PreferredLanguage = .auto) {
        let channel = SavedChannel(name: name, url: url, videoCount: videoCount, dateAdded: Date(), thumbnailURL: thumbnailURL, liveVideoLimit: liveVideoLimit, regularVideoLimit: regularVideoLimit, sourceType: sourceType, preferredLanguage: preferredLanguage)
        savedChannels.append(channel)
        saveSavedChannels()
    }

    func updateChannelLastDownloaded(_ channelId: UUID) {
        if let index = savedChannels.firstIndex(where: { $0.id == channelId }) {
            let channel = savedChannels[index]
            let updatedChannel = SavedChannel(
                id: channel.id,
                name: channel.name,
                url: channel.url,
                videoCount: channel.videoCount,
                dateAdded: channel.dateAdded,
                thumbnailURL: channel.thumbnailURL,
                liveVideoLimit: channel.liveVideoLimit,
                regularVideoLimit: channel.regularVideoLimit,
                lastDownloaded: Date(),
                sourceType: channel.sourceType,
                preferredLanguage: channel.preferredLanguage
            )
            savedChannels[index] = updatedChannel
            saveSavedChannels()
        }
    }

    func updateChannel(_ updatedChannel: SavedChannel) {
        if let index = savedChannels.firstIndex(where: { $0.id == updatedChannel.id }) {
            savedChannels[index] = updatedChannel
            saveSavedChannels()
        }
    }

    private var channelsFileURL: URL {
        return appSupportDirectory.appendingPathComponent("channels.json")
    }

    private func loadSavedChannels() {
        // First try to load from file (more persistent)
        if FileManager.default.fileExists(atPath: channelsFileURL.path) {
            do {
                let data = try Data(contentsOf: channelsFileURL)
                let channels = try JSONDecoder().decode([SavedChannel].self, from: data)
                self.savedChannels = channels
                print("Loaded \(channels.count) channels from file")
                return
            } catch {
                print("Error loading channels from file: \(error)")
            }
        }

        // Fallback to UserDefaults (for backward compatibility)
        if let data = UserDefaults.standard.data(forKey: "SavedChannels"),
           let channels = try? JSONDecoder().decode([SavedChannel].self, from: data) {
            self.savedChannels = channels
            print("Loaded \(channels.count) channels from UserDefaults")
            // Migrate to file storage
            saveSavedChannels()
        }
    }

    private func saveSavedChannels() {
        do {
            let data = try JSONEncoder().encode(savedChannels)

            // Save to file (primary storage)
            try data.write(to: channelsFileURL)

            // Also save to UserDefaults (backup)
            UserDefaults.standard.set(data, forKey: "SavedChannels")

            print("Saved \(savedChannels.count) channels to persistent storage")
        } catch {
            print("Error saving channels: \(error)")
        }
    }

    func removeChannel(_ id: UUID) {
        savedChannels.removeAll { $0.id == id }
        saveSavedChannels()
    }

    // MARK: - URL Detection and Validation

    func detectSourceType(from url: String) -> SourceType {
        let lowercaseURL = url.lowercased()

        // Check for playlist patterns
        if lowercaseURL.contains("playlist?list=") ||
           (lowercaseURL.contains("watch?") && lowercaseURL.contains("list=")) ||
           (lowercaseURL.contains("youtu.be/") && lowercaseURL.contains("list=")) {
            return .playlist
        }

        // Check for channel patterns
        if lowercaseURL.contains("youtube.com/channel/") ||
           lowercaseURL.contains("youtube.com/c/") ||
           lowercaseURL.contains("youtube.com/@") ||
           lowercaseURL.contains("youtube.com/user/") {
            return .channel
        }

        // Default to channel for ambiguous cases
        return .channel
    }

    func isValidYouTubeURL(_ url: String) -> Bool {
        let lowercaseURL = url.lowercased()

        // Check for valid YouTube URL patterns
        let validPatterns = [
            "youtube.com/channel/",
            "youtube.com/c/",
            "youtube.com/@",
            "youtube.com/user/",
            "youtube.com/playlist?list=",
            "youtube.com/watch?",
            "youtu.be/"
        ]

        return validPatterns.contains { lowercaseURL.contains($0) }
    }

    // MARK: - Playlist Support

    func getPlaylistVideos(_ playlistURL: String, maxVideos: Int = 50, completion: @escaping (Result<[VideoInfo], Error>) -> Void) {
        let scriptPath = Bundle.main.path(forResource: "get_playlist_videos", ofType: "py")!

        let task = Process()
        task.executableURL = URL(fileURLWithPath: "/usr/bin/python3")
        task.arguments = [scriptPath, playlistURL, String(maxVideos)]

        let pipe = Pipe()
        let errorPipe = Pipe()
        task.standardOutput = pipe
        task.standardError = errorPipe

        task.terminationHandler = { _ in
            let data = pipe.fileHandleForReading.readDataToEndOfFile()
            let errorData = errorPipe.fileHandleForReading.readDataToEndOfFile()
            let output = String(data: data, encoding: .utf8) ?? ""
            let errorOutput = String(data: errorData, encoding: .utf8) ?? ""

            print("Playlist script output: \(output)")
            if !errorOutput.isEmpty {
                print("Playlist script stderr: \(errorOutput)")
            }

            DispatchQueue.main.async {
                do {
                    if let jsonData = output.data(using: .utf8),
                       let response = try JSONSerialization.jsonObject(with: jsonData) as? [String: Any] {

                        if let success = response["success"] as? Bool, success,
                           let videosArray = response["videos"] as? [[String: Any]] {

                            let videos = videosArray.compactMap { videoDict -> VideoInfo? in
                                guard let title = videoDict["title"] as? String,
                                      let url = videoDict["url"] as? String else {
                                    return nil
                                }

                                return VideoInfo(
                                    title: title,
                                    url: url,
                                    videoId: videoDict["video_id"] as? String ?? "",
                                    author: videoDict["author"] as? String ?? "",
                                    duration: videoDict["duration"] as? Int ?? 0,
                                    viewCount: videoDict["view_count"] as? Int ?? 0,
                                    publishDate: videoDict["publish_date"] as? String
                                )
                            }

                            completion(.success(videos))
                        } else {
                            let errorMessage = response["error"] as? String ?? "Unknown error getting playlist videos"
                            completion(.failure(NSError(domain: "PlaylistError", code: 1, userInfo: [NSLocalizedDescriptionKey: errorMessage])))
                        }
                    } else {
                        completion(.failure(NSError(domain: "PlaylistError", code: 2, userInfo: [NSLocalizedDescriptionKey: "Failed to parse playlist response"])))
                    }
                } catch {
                    completion(.failure(error))
                }
            }
        }

        do {
            try task.run()
        } catch {
            completion(.failure(error))
        }
    }

    // MARK: - Import/Export Functionality

    func exportChannels() -> URL? {
        let exportData = ExportData(
            channels: savedChannels,
            exportDate: Date(),
            appVersion: "1.0"
        )

        do {
            let data = try JSONEncoder().encode(exportData)
            let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
            let exportURL = documentsPath.appendingPathComponent("YouTubeChannels_\(DateFormatter.exportFormatter.string(from: Date())).json")

            try data.write(to: exportURL)
            return exportURL
        } catch {
            print("Export error: \(error)")
            return nil
        }
    }

    func importChannels(from url: URL) -> ImportResult {
        do {
            let data = try Data(contentsOf: url)
            let exportData = try JSONDecoder().decode(ExportData.self, from: data)

            var importedCount = 0
            var duplicateCount = 0

            for channel in exportData.channels {
                // Check for duplicates by URL
                if !savedChannels.contains(where: { $0.url == channel.url }) {
                    savedChannels.append(channel)
                    importedCount += 1
                } else {
                    duplicateCount += 1
                }
            }

            if importedCount > 0 {
                saveSavedChannels()
            }

            return ImportResult(
                success: true,
                importedCount: importedCount,
                duplicateCount: duplicateCount,
                totalCount: exportData.channels.count
            )

        } catch {
            return ImportResult(
                success: false,
                importedCount: 0,
                duplicateCount: 0,
                totalCount: 0,
                error: error.localizedDescription
            )
        }
    }
}

// MARK: - Import/Export Data Structures

struct ExportData: Codable {
    let channels: [SavedChannel]
    let exportDate: Date
    let appVersion: String
}

struct ImportResult {
    let success: Bool
    let importedCount: Int
    let duplicateCount: Int
    let totalCount: Int
    let error: String?

    init(success: Bool, importedCount: Int, duplicateCount: Int, totalCount: Int, error: String? = nil) {
        self.success = success
        self.importedCount = importedCount
        self.duplicateCount = duplicateCount
        self.totalCount = totalCount
        self.error = error
    }
}

extension DateFormatter {
    static let exportFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd_HH-mm-ss"
        return formatter
    }()
}


