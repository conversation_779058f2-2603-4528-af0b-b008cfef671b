#!/usr/bin/env python3

import sys
from pytubefix import Channel

def test_live_streams():
    """Test what's actually in the live streams"""
    
    channel_url = "https://www.youtube.com/@SteveRam"
    
    try:
        print(f"Testing live streams for: {channel_url}")
        channel = Channel(channel_url)
        
        print(f"\nChannel Name: {channel.channel_name}")
        
        # Test channel.live
        print("\n=== TESTING channel.live ===")
        try:
            live_count = 0
            for video in channel.live:
                live_count += 1
                video_id = getattr(video, 'video_id', 'No ID')
                title = getattr(video, 'title', 'No title')
                print(f"Live {live_count}: {title} (ID: {video_id})")
                if live_count >= 10:
                    break
            print(f"Total in channel.live: {live_count}")
        except Exception as e:
            print(f"Error with channel.live: {e}")
        
        # Test channel.lives
        print("\n=== TESTING channel.lives ===")
        try:
            lives_count = 0
            for video in channel.lives:
                lives_count += 1
                video_id = getattr(video, 'video_id', 'No ID')
                title = getattr(video, 'title', 'No title')
                print(f"Lives {lives_count}: {title} (ID: {video_id})")
                if lives_count >= 10:
                    break
            print(f"Total in channel.lives: {lives_count}")
        except Exception as e:
            print(f"Error with channel.lives: {e}")
        
        # Test regular videos for comparison
        print("\n=== TESTING channel.video_urls (regular) ===")
        try:
            regular_count = 0
            for video in channel.video_urls:
                regular_count += 1
                video_id = getattr(video, 'video_id', 'No ID')
                title = getattr(video, 'title', 'No title')
                print(f"Regular {regular_count}: {title} (ID: {video_id})")
                if regular_count >= 5:
                    break
            print(f"Total in channel.video_urls: {regular_count}")
        except Exception as e:
            print(f"Error with channel.video_urls: {e}")
            
        # Test specific known live stream IDs
        print("\n=== CHECKING FOR KNOWN LIVE STREAM IDs ===")
        known_live_ids = ['JcSTJ5ufvX8', 'QhczRPoyBzw', 'ybIhXpvzGIA']
        
        for source_name, source in [('live', channel.live), ('lives', channel.lives)]:
            print(f"\nChecking {source_name} for known IDs...")
            try:
                found_ids = []
                for video in source:
                    video_id = getattr(video, 'video_id', None)
                    if video_id in known_live_ids:
                        title = getattr(video, 'title', 'No title')
                        found_ids.append(f"{video_id}: {title}")
                
                if found_ids:
                    print(f"Found in {source_name}: {found_ids}")
                else:
                    print(f"None of the known live IDs found in {source_name}")
            except Exception as e:
                print(f"Error checking {source_name}: {e}")
                
    except Exception as e:
        print(f"Error creating channel: {e}")

if __name__ == "__main__":
    test_live_streams()
