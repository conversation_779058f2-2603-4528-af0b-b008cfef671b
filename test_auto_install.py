#!/usr/bin/env python3
"""
Test script to verify automatic pytubefix installation
"""

import sys
import subprocess

# Simulate missing pytubefix by temporarily uninstalling it
print("Testing automatic dependency installation...")

# Test the enhanced download script
test_url = "https://www.youtube.com/watch?v=dQw4w9WgXcQ"  # Rick Roll for testing
result = subprocess.run([
    sys.executable, 
    "/Applications/Scripture.app/Contents/Resources/download_transcript_enhanced.py",
    test_url,
    "/tmp/test_output",
    "Test Video"
], capture_output=True, text=True)

print("Exit code:", result.returncode)
print("STDOUT:", result.stdout)
print("STDERR:", result.stderr)
